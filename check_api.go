package main

import (
	"fmt"
	"reflect"

	kargov1 "github.com/akuity/api-client-go/pkg/api/gen/kargo/v1"
)

func main() {
	req := &kargov1.ApplyKargoInstanceRequest{}
	t := reflect.TypeOf(req).Elem()

	fmt.Println("ApplyKargoInstanceRequest fields:")
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		fmt.Printf("  %s: %s\n", field.Name, field.Type)
	}

	fmt.Println("\nExportKargoInstanceResponse fields:")
	resp := &kargov1.ExportKargoInstanceResponse{}
	t2 := reflect.TypeOf(resp).Elem()
	for i := 0; i < t2.NumField(); i++ {
		field := t2.Field(i)
		fmt.Printf("  %s: %s\n", field.Name, field.Type)
	}
}
