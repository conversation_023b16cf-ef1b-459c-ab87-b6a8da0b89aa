---
# generated by https://github.com/hashicorp/terraform-plugin-docs
page_title: "akp_kargo_agents Data Source - akp"
subcategory: ""
description: |-
  Gets information about all Kargo agents attached to an Argo CD instance
---

# akp_kargo_agents (Data Source)

Gets information about all Kargo agents attached to an Argo CD instance

## Example Usage

```terraform
data "akp_kargo_instance" "example" {
  name = "test"
}

data "akp_kargo_agents" "examples" {
  instance_id = data.akp_kargo_instance.example.id
}
```

<!-- schema generated by tfplugindocs -->
## Schema

### Required

- `instance_id` (String) Kargo instance ID

### Read-Only

- `agents` (Attributes List) List of Kargo agents (see [below for nested schema](#nestedatt--agents))
- `id` (String) Kaego instance ID

<a id="nestedatt--agents"></a>
### Nested Schema for `agents`

Required:

- `instance_id` (String) The ID of the Kargo instance
- `name` (String) The name of the Kargo agent

Read-Only:

- `annotations` (Map of String) The annotations of the Kargo agent
- `id` (String) The ID of the Kargo agent
- `kube_config` (Attributes) The kubeconfig of the Kargo agent (see [below for nested schema](#nestedatt--agents--kube_config))
- `labels` (Map of String) The labels of the Kargo agent
- `namespace` (String) The namespace of the Kargo agent
- `remove_agent_resources_on_destroy` (Boolean) Whether to remove agent resources on destroy
- `spec` (Attributes) The spec of the Kargo agent (see [below for nested schema](#nestedatt--agents--spec))
- `workspace` (String) Workspace name for the Kargo agent

<a id="nestedatt--agents--kube_config"></a>
### Nested Schema for `agents.kube_config`

Read-Only:

- `client_certificate` (String) PEM-encoded client certificate for TLS authentication.
- `client_key` (String, Sensitive) PEM-encoded client certificate key for TLS authentication.
- `cluster_ca_certificate` (String) PEM-encoded root certificates bundle for TLS authentication.
- `config_context` (String) Context name to load from the kube config file.
- `config_context_auth_info` (String)
- `config_context_cluster` (String)
- `config_path` (String) Path to the kube config file.
- `config_paths` (List of String) A list of paths to kube config files.
- `host` (String) The hostname (in form of URI) of Kubernetes master.
- `insecure` (Boolean) Whether server should be accessed without verifying the TLS certificate.
- `password` (String, Sensitive) The password to use for HTTP basic authentication when accessing the Kubernetes master endpoint.
- `proxy_url` (String) URL to the proxy to be used for all API requests
- `token` (String, Sensitive) Token to authenticate an service account
- `username` (String) The username to use for HTTP basic authentication when accessing the Kubernetes master endpoint.


<a id="nestedatt--agents--spec"></a>
### Nested Schema for `agents.spec`

Read-Only:

- `data` (Attributes) The data of the Kargo agent (see [below for nested schema](#nestedatt--agents--spec--data))
- `description` (String) The description of the Kargo agent

<a id="nestedatt--agents--spec--data"></a>
### Nested Schema for `agents.spec.data`

Read-Only:

- `akuity_managed` (Boolean) Whether the Kargo agent is managed by Akuity
- `argocd_namespace` (String) The namespace of the Argo CD instance
- `auto_upgrade_disabled` (Boolean) Whether auto upgrade is disabled
- `kustomization` (String) Kustomize configuration that will be applied to generated Kargo agent installation manifests
- `remote_argocd` (String) The ID of the remote Argo CD instance
- `size` (String) The size of the Kargo agent
- `target_version` (String) The target version of the Kargo agent
