---
# generated by https://github.com/hashicorp/terraform-plugin-docs
page_title: "akp_kargo_instance Data Source - akp"
subcategory: ""
description: |-
  Gets information about a Kargo instance
---

# akp_kargo_instance (Data Source)

Gets information about a Kargo instance

## Example Usage

```terraform
data "akp_kargo_instance" "example" {
  name = "test"
}
```

<!-- schema generated by tfplugindocs -->
## Schema

### Required

- `name` (String) Kargo instance name

### Read-Only

- `id` (String) Kargo instance ID
- `kargo` (Attributes) Specification of the Kargo instance (see [below for nested schema](#nestedatt--kargo))
- `kargo_cm` (Map of String) ConfigMap to configure system account accesses. The usage can be found in the examples/resources/akp_kargo_instance/resource.tf
- `kargo_resources` (List of String) List of Kargo custom resources to be managed alongside the Kargo instance. Currently supported resources are: `Project`, `ClusterPromotionTask`, `Stage`, `Warehouse`, `AnalysisTemplate`, `PromotionTask`. Should all be in the apiVersion `kargo.akuity.io/v1alpha1`.
- `kargo_secret` (Map of String) Secret to configure system account accesses. The usage can be found in the examples/resources/akp_kargo_instance/resource.tf
- `workspace` (String) Workspace name for the Kargo instance

<a id="nestedatt--kargo"></a>
### Nested Schema for `kargo`

Read-Only:

- `spec` (Attributes) Kargo instance spec (see [below for nested schema](#nestedatt--kargo--spec))

<a id="nestedatt--kargo--spec"></a>
### Nested Schema for `kargo.spec`

Read-Only:

- `description` (String) Description of the Kargo instance
- `fqdn` (String) FQDN of the Kargo instance
- `kargo_instance_spec` (Attributes) Kargo instance specific configuration (see [below for nested schema](#nestedatt--kargo--spec--kargo_instance_spec))
- `oidc_config` (Attributes) OIDC configuration (see [below for nested schema](#nestedatt--kargo--spec--oidc_config))
- `subdomain` (String) Subdomain of the Kargo instance
- `version` (String) Version of the Kargo instance

<a id="nestedatt--kargo--spec--kargo_instance_spec"></a>
### Nested Schema for `kargo.spec.kargo_instance_spec`

Read-Only:

- `agent_customization_defaults` (Attributes) Default agent customization settings (see [below for nested schema](#nestedatt--kargo--spec--kargo_instance_spec--agent_customization_defaults))
- `backend_ip_allow_list_enabled` (Boolean) Whether IP allow list is enabled for the backend
- `default_shard_agent` (String) Default shard agent
- `global_credentials_ns` (List of String) List of global credentials namespaces
- `global_service_account_ns` (List of String) List of global service account namespaces
- `ip_allow_list` (Attributes List) List of allowed IPs (see [below for nested schema](#nestedatt--kargo--spec--kargo_instance_spec--ip_allow_list))

<a id="nestedatt--kargo--spec--kargo_instance_spec--agent_customization_defaults"></a>
### Nested Schema for `kargo.spec.kargo_instance_spec.agent_customization_defaults`

Read-Only:

- `auto_upgrade_disabled` (Boolean) Whether auto upgrade is disabled
- `kustomization` (String) Kustomization configuration


<a id="nestedatt--kargo--spec--kargo_instance_spec--ip_allow_list"></a>
### Nested Schema for `kargo.spec.kargo_instance_spec.ip_allow_list`

Read-Only:

- `description` (String) Description for the IP address
- `ip` (String) IP address



<a id="nestedatt--kargo--spec--oidc_config"></a>
### Nested Schema for `kargo.spec.oidc_config`

Read-Only:

- `additional_scopes` (List of String) Additional scopes
- `admin_account` (Attributes) Admin account (see [below for nested schema](#nestedatt--kargo--spec--oidc_config--admin_account))
- `cli_client_id` (String) CLI Client ID
- `client_id` (String) Client ID
- `dex_config` (String) DEX configuration
- `dex_config_secret` (Map of String) DEX configuration secret
- `dex_enabled` (Boolean) Whether DEX is enabled
- `enabled` (Boolean) Whether OIDC is enabled
- `issuer_url` (String) Issuer URL
- `viewer_account` (Attributes) Viewer account (see [below for nested schema](#nestedatt--kargo--spec--oidc_config--viewer_account))

<a id="nestedatt--kargo--spec--oidc_config--admin_account"></a>
### Nested Schema for `kargo.spec.oidc_config.admin_account`

Read-Only:

- `claims` (Attributes Map) Claims (see [below for nested schema](#nestedatt--kargo--spec--oidc_config--admin_account--claims))

<a id="nestedatt--kargo--spec--oidc_config--admin_account--claims"></a>
### Nested Schema for `kargo.spec.oidc_config.admin_account.claims`

Read-Only:

- `values` (List of String)



<a id="nestedatt--kargo--spec--oidc_config--viewer_account"></a>
### Nested Schema for `kargo.spec.oidc_config.viewer_account`

Read-Only:

- `claims` (Attributes Map) Claims (see [below for nested schema](#nestedatt--kargo--spec--oidc_config--viewer_account--claims))

<a id="nestedatt--kargo--spec--oidc_config--viewer_account--claims"></a>
### Nested Schema for `kargo.spec.oidc_config.viewer_account.claims`

Read-Only:

- `values` (List of String)
