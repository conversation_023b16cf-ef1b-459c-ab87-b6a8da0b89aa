---
# generated by https://github.com/hashicorp/terraform-plugin-docs
page_title: "akp Provider"
subcategory: ""
description: |-
  
---

# akp Provider



## Example Usage

```terraform
provider "akp" {
  org_name = "organization-name"
}
```

<!-- schema generated by tfplugindocs -->
## Schema

### Required

- `org_name` (String) Organization Name

### Optional

- `api_key_id` (String, Sensitive) API Key Id. Use environment variable `AKUITY_API_KEY_ID`
- `api_key_secret` (String, Sensitive) API Key Secret, Use environment variable `AKUITY_API_KEY_SECRET`
- `server_url` (String) Akuity Platform API URL, default: `https://akuity.cloud`. You can use environment variable `AKUITY_SERVER_URL` instead
- `skip_tls_verify` (Boolean) Skip TLS Verify. Only use for testing self-hosted version
