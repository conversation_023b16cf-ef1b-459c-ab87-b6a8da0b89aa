---
page_title: "Terraform AKP Provider version 0.8 upgrading guide"
subcategory: ""
description: Terraform AKP Provider Version 0.8 Upgrade Guide
---

# Terraform AKP Provider Version 0.8 Upgrade Guide

Version 0.8.0 of the AKP provider for Terraform includes changes that you need to consider when upgrading. This guide
will help with that process and focuses on changes from version 0.7.x to version 0.8.0.

## Provider Version Configuration

-> Before upgrading to version 0.8.0, upgrade to the most recent 0.7.x version (0.7.3) of the provider and ensure that
your environment successfully runs [`terraform plan`](https://www.terraform.io/docs/commands/plan.html). You should not
see changes you don't expect or deprecation notices. Also, back up your `.tf` and `.tfstate` files prior to starting the
upgrade process.

Use [version constraints when configuring Terraform providers](https://www.terraform.io/docs/configuration/providers.html#provider-versions).
If you are following that recommendation, update the version constraints in your Terraform configuration and
run [`terraform init -upgrade`](https://www.terraform.io/docs/commands/init.html) to download the new version.

For example, given this previous configuration:

```terraform
terraform {
  required_providers {
    akp = {
      source  = "akuity/akp"
      version = "~> 0.7.3"
    }
  }
}

provider "akp" {
  # Configuration options
}
```

Update to the latest 0.8.0 version:

```terraform
terraform {
  required_providers {
    akp = {
      source  = "akuity/akp"
      version = "~> 0.8.0"
    }
  }
}

provider "akp" {
  # Configuration options
}
```

### akp_kargo_instance

The `akp_kargo_instance` resource allows you to manage Kargo instances with the following key fields:

| Field                                                   | Description                                                                                                                    |
|---------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------|
| `name (String)`                                         | (Required) Name of the Kargo instance                                                                                          |
| `kargo_cm (Map)`                                        | (Optional) ConfigMap to configure system account access settings like `adminAccountEnabled` and `adminAccountTokenTtl`         |
| `kargo_secret (Map)`                                    | (Optional) Secret configurations for system accounts like `adminAccountPasswordHash`                                           |
| `kargo.spec.version (String)`                           | (Required) Version of the Kargo instance                                                                                       |
| `kargo.spec.description (String)`                       | (Optional) Description of the Kargo instance                                                                                   |
| `kargo.spec.fqdn (String)`                             | (Optional) Fully qualified domain name for the instance                                                                        |
| `kargo.spec.subdomain (String)`                        | (Optional) Subdomain for the instance (mutually exclusive with fqdn)                                                          |
| `kargo.spec.oidc_config (Attributes)`                       | (Optional) OIDC configuration including Dex integration options                                                                |
| `kargo.spec.kargo_instance_spec.backend_ip_allow_list_enabled (Boolean)` | (Optional) Enable backend IP allowlist for the instance                                                    |
| `kargo.spec.kargo_instance_spec.ip_allow_list (Attributes List)` | (Optional) Configure IP allowlist entries with IP addresses and descriptions                                                   |
| `kargo.spec.kargo_instance_spec.agent_customization_defaults (Attributes)` | (Optional) Default settings for agent customization including auto-upgrade and kustomization                    |
| `kargo.spec.kargo_instance_spec.default_shard_agent (String)` | (Optional) Default shard agent for the instance                                                                               |
| `kargo.spec.kargo_instance_spec.global_credentials_ns (String)` | (Optional) Namespace for global credentials                                                                                   |
| `kargo.spec.kargo_instance_spec.global_service_account_ns (String)` | (Optional) Namespace for global service accounts                                                                             |

Example configuration:

```terraform
resource "akp_kargo_instance" "example" {
  name = "test"
  kargo_cm = {
    adminAccountEnabled  = "true"
    adminAccountTokenTtl = "24h"
  }
  kargo_secret = {
    adminAccountPasswordHash = "$2a$10$wThs/VVwx5Tbygkk5Rzbv.V8hR8JYYmRdBiGjue9pd0YcEXl7.Kn."
  }
  kargo = {
    spec = {
      description = "test-description"
      version     = "v1.1.1"
      // only set one of fqdn and subdomain
      fqdn      = "fqdn.example.com"
      subdomain = ""
      oidc_config = {
        enabled     = true
        dex_enabled = false
        # client_id should be set only if dex_enabled is false
        client_id = "test-client-id"
        # client_secret should be set only if dex_enabled is false
        cli_client_id = "test-cli-client-id"
        # issuer_url should be set only if dex_enabled is false
        issuer_url = "https://test.com"
        # additional_scopes should be set only if dex_enabled is false
        additional_scopes = ["test-scope"]
        # dex_secret should be set only if dex_enabled is false
        dex_secret = {
          name = "test-secret"
        }
        # dex_config should be set only if dex_enabled is true, and if dex is set, then oidc related fields should not be set
        dex_config = ""
        admin_account = {
          claims = {
            groups = {
              values = ["<EMAIL>"]
            }
            email = {
              values = ["<EMAIL>"]
            }
            sub = {
              values = ["<EMAIL>"]
            }
          }
        }
        viewer_account = {
          claims = {
            groups = {
              values = ["<EMAIL>"]
            }
            email = {
              values = ["<EMAIL>"]
            }
            sub = {
              values = ["<EMAIL>"]
            }
          }
        }
      }
      kargo_instance_spec = {
        backend_ip_allow_list_enabled = true
        ip_allow_list = [
          {
            ip          = "***********"
            description = "test-description"
          }
        ]
        agent_customization_defaults = {
          auto_upgrade_disabled = true
          kustomization         = <<-EOT
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
images:
  - name: ghcr.io/akuity/kargo
    newName: quay.io/akuityy/kargo
  - name: quay.io/akuityio/argo-rollouts
    newName: quay.io/akuityy/argo-rollouts
  - name: quay.io/akuity/agent
    newName: quay.io/akuityy/agent
EOT
        }
        default_shard_agent       = "test"
        global_credentials_ns     = ["test1", "test2"]
        global_service_account_ns = ["test3", "test4"]
      }
    }
  }
}
```

### akp_kargo_agent

The `akp_kargo_agent` resource enables management of Kargo agents with the following key fields:

| Field                                         | Description                                                                                       |
|-----------------------------------------------|---------------------------------------------------------------------------------------------------|
| `instance_id (String)`                        | (Required) ID of the parent Kargo instance                                                        |
| `name (String)`                               | (Required) Name of the Kargo agent                                                                |
| `namespace (String)`                          | (Optional) Kubernetes namespace for the agent                                                     |
| `labels (Map)`                                | (Optional) Labels to apply to the agent                                                           |
| `annotations (Map)`                           | (Optional) Annotations to apply to the agent                                                      |
| `spec.description (String)`                   | (Optional) Description of the agent                                                               |
| `spec.data.target_version (String)`           | (Optional) Target version for the agent                                                           |
| `spec.data.size (String)`                     | (Required) Cluster size. One of `small`, `medium`, `large`                                        |
| `spec.data.akuity_managed (Boolean)`          | (Optional) Whether the agent is managed by Akuity                                                 |
| `spec.data.remote_argocd (String)`            | (Optional) Remote Argo CD instance to connect to                                                  |
| `spec.data.argocd_namespace (String)`         | (Optional) Namespace of ArgoCD installation for self-hosted mode                                  |
| `spec.data.auto_upgrade_disabled (Boolean)`   | (Optional) Disable automatic upgrades of the agent                                                |
| `spec.data.kustomization (String)`            | (Optional) Kustomize configuration that will be applied to generated agent installation manifests |
| `kube_config (Attributes)`                           | (Optional) Kubernetes configuration for the agent installation                                    |
| `remove_agent_resources_on_destroy (Boolean)` | (Optional) Remove agent resources on destroy                                                    |

You can manage these settings in Terraform by adding the above fields to your `akp_kargo_instance` and `akp_kargo_agent` resources.

Example configuration:

```terraform
resource "akp_kargo_agent" "example-agent" {
  instance_id = akp_kargo_instance.example.id
  name        = "test-agent"
  namespace   = "test-namespace"
  labels = {
    "app" = "kargo"
  }
  annotations = {
    "app" = "kargo"
  }
  spec = {
    description = "test-description"
    data = {
      size = "medium"
      // Akuity managed agent. For self-hosted agents, the fields can be set are in example documentation.
      akuity_managed = true
      # this needs to be the ArgoCD instance ID, and once it is set, it should not be changed.
      remote_argocd = "<your_argocd_instance_id>" # Replace with your actual ArgoCD instance ID
    }
  }
}
```

These new resources provide comprehensive management capabilities for Kargo within your infrastructure. Refer to the resource documentation for detailed attribute descriptions and usage examples.

## Apply the change

After updating your configuration, run `terraform plan` again to verify no unintended changes were introduced. If you modified existing fields or added new 0.8.0 fields, review the planned changes before running `terraform apply` to apply the updates.
