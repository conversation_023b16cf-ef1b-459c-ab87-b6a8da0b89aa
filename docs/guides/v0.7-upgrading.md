---
page_title: "Terraform AKP Provider version 0.7 upgrading guide"
subcategory: ""
description: Terraform AKP Provider Version 0.7 Upgrade Guide
---

# Terraform AKP Provider Version 0.7 Upgrade Guide

Version 0.7.0 of the AKP provider for Terraform includes changes that you need to consider when upgrading. This guide
will help with that process and focuses on changes from version 0.6.x to version 0.7.0.

## Provider Version Configuration

-> Before upgrading to version 0.7.0, upgrade to the most recent 0.6.x version (0.6.3) of the provider and ensure that
your environment successfully runs [`terraform plan`](https://www.terraform.io/docs/commands/plan.html). You should not
see changes you don't expect or deprecation notices. Also, back up your `.tf` and `.tfstate` files prior to starting the
upgrade process.

Use [version constraints when configuring Terraform providers](https://www.terraform.io/docs/configuration/providers.html#provider-versions).
If you are following that recommendation, update the version constraints in your Terraform configuration and
run [`terraform init -upgrade`](https://www.terraform.io/docs/commands/init.html) to download the new version.

For example, given this previous configuration:

```terraform
terraform {
  required_providers {
    akp = {
      source  = "akuity/akp"
      version = "~> 0.6.3"
    }
  }
}

provider "akp" {
  # Configuration options
}
```

Update to the latest 0.7.0 version:

```terraform
terraform {
  required_providers {
    akp = {
      source  = "akuity/akp"
      version = "~> 0.7.0"
    }
  }
}

provider "akp" {
  # Configuration options
}
```

## akp_instance

### New Fields

Version 0.7.0 introduces the following new fields on `akp_instance` resource and data source:

| Field                                            | Description                                                                                                                                                                                                                       |
|--------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `spec.agent_permissions_rules (Attributes List)` | (Optional) The ability to configure agent permissions rules.                                                                                                                                                                      |
| `spec.crossplane_extension (Attributes) `        | (Optional) Custom Resource Definition group name that identifies the Crossplane resource in kubernetes. We will include built-in crossplane resources. Note that you can use glob pattern to match the group. ie. *.crossplane.io |
| `spec.fqdn (String)`                             | (Optional) Configures the FQDN for the argocd instance, for ingress URL, domain suffix, etc.                                                                                                                                      |

You can manage these settings in Terraform by adding the above fields to your `akp_instance` resource and data source.

### Improvements

Before version 0.7.0, the `argocd_cm` attribute in the `akp_instance` resource had nested fields
like `application.links`, and these fields may not be applied correctly due to the inconsistency in the Terraform
provider. In version 0.7.0, we have improved the provider to handle these attributes correctly.

## akp_cluster

### New Fields

Version 0.7.0 introduces the following new fields on `akp_cluster` resource and data source:

| Field                                             | Description                                                                                                                                  |
|---------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------|
| `spec.data.datadog_annotations_enabled (Boolean)` | (Optional) Enable Datadog metrics collection of Application Controller and Repo Server. Make sure that you install Datadog agent in cluster. |
| `spec.data.eks_addon_enabled (Boolean) `          | (Optional) Enable this if you are installing this cluster on EKS.                                                                            |
| `spec.data.managed_cluster_config (Attributes)`   | (Optional) The config to access managed Kubernetes cluster. By default agent is using "in-cluster" config.                                   |

## Apply the change

After updating your configuration, run `terraform plan` again to verify no unintended changes were introduced. If you
modified existing fields or added new 0.7.0 fields, review the planned changes before running `terraform apply` to apply
the updates.
