---
page_title: "Terraform AKP Provider version 0.6 upgrading guide"
subcategory: ""
description: Terraform AKP Provider Version 0.6 Upgrade Guide
---
# Terraform AKP Provider Version 0.6 Upgrade Guide
Version 0.6.0 of the AKP provider for Terraform includes changes that you need to consider when upgrading. This guide will help with that process and focuses on changes from version 0.5.x to version 0.6.0.

## Provider Version Configuration

-> Before upgrading to version 0.6.0, upgrade to the most recent 0.5.x version (0.5.5) of the provider and ensure that your environment successfully runs [`terraform plan`](https://www.terraform.io/docs/commands/plan.html). You should not see changes you don't expect or deprecation notices. Also, back up your `.tf` and `.tfstate` files prior to starting the upgrade process.

Use [version constraints when configuring Terraform providers](https://www.terraform.io/docs/configuration/providers.html#provider-versions). If you are following that recommendation, update the version constraints in your Terraform configuration and run [`terraform init -upgrade`](https://www.terraform.io/docs/commands/init.html) to download the new version.

For example, given this previous configuration:

```terraform
terraform {
  required_providers {
    akp = {
      source  = "akuity/akp"
      version = "~> 0.5.0"
    }
  }
}

provider "akp" {
  # Configuration options
}
```

Update to the latest 0.6.x version:

```terraform
terraform {
  required_providers {
    akp = {
      source  = "akuity/akp"
      version = "~> 0.6.0"
    }
  }
}

provider "akp" {
  # Configuration options
}
```

## akp_instance

### ConfigMaps

Version 0.6.0 reduces the unnecessary nested `data` field of the following attributes:

- `argocd_cm`
- `argocd_rbac_cm`
- `argocd_notifications_cm`
- `argocd_image_updater_ssh_config`
- `argocd_image_updater_config`
- `argocd_ssh_known_hosts_cm`
- `argocd_tls_certs_cm`

For example, given this version 0.5.x `argocd_cm` attribute in `akp_instance` resource:

```hcl
resource "akp_instance" "example" {
  name = "argocd"
  // ...
  argocd_cm = {
    data = {
      "admin.enabled"                  = false
      "exec.enabled"                   = true
      "ga.anonymizeusers"              = false
      "helm.enabled"                   = true
      "kustomize.enabled"              = true
      "server.rbac.log.enforce.enable" = false
      "statusbadge.enabled"            = false
      "ui.bannerpermanent"             = false
      "users.anonymous.enabled"        = true
    }
  }
  // ...
}
```

The version 0.6.0 equivalent is:

```hcl
resource "akp_instance" "example" {
  name = "argocd"
  // ...
  argocd_cm = {
    "admin.enabled"                  = false
    "exec.enabled"                   = true
    "ga.anonymizeusers"              = false
    "helm.enabled"                   = true
    "kustomize.enabled"              = true
    "server.rbac.log.enforce.enable" = false
    "statusbadge.enabled"            = false
    "ui.bannerpermanent"             = false
    "users.anonymous.enabled"        = true
  }
  // ...
}
```
Update existing `akp_instance` resource based on the above example for all the mentioned attributes.

### Secrets

Version 0.6.0 reduces the following unnecessary nested fields:
- `data`
- `labels`
- `name`
- `string_data`
- `type`

of the following attributes:
- `argocd_secret`
- `argocd_notifications_secret`
- `argocd_image_updater_secret`
- `repo_credential_secrets`
- `repo_template_credential_secrets`

and `repo_credential_secrets` and `repo_template_credential_secrets` are changed from a `List` of secrets to a `Map` of secrets.

For example, given this version 0.5.x `argocd_secret` and `repo_credential_secrets` in `akp_instance` resource:
```hcl
resource "akp_instance" "example" {
  name = "argocd"
  // ...
  argocd_secret = {
    type = "Opaque"
    string_data = {
      "dex.github.clientSecret" = "my-github-oidc-secret"
      "webhook.github.secret"   = "shhhh! it'   s a github secret"
    }
  }
  // ...
  repo_credential_secrets = [
    {
      name      = "repo-my-private-https-repo"
      namespace = "argocd"

      labels = {
        "argocd.argoproj.io/secret-type" = "repository"
      }

      string_data = {
        url                = "https://github.com/argoproj/argocd-example-apps"
        password           = "my-ppassword"
        username           = "my-username"
        insecure           = true
        forceHttpBasicAuth = true
        enableLfs          = true
      }
    },
    {
      name      = "repo-my-private-ssh-repo"
      namespace = "argocd"

      labels = {
        "argocd.argoproj.io/secret-type" = "repository"
      }

      string_data = {
        url           = "ssh://**************/argoproj/argocd-example-apps"
        sshPrivateKey = <<EOF
      # paste the sshPrivateKey data here
      EOF
        insecure      = true
        enableLfs     = true
      }
    }
  ]
}
```

The version 0.6.0 equivalent is:
```hcl
resource "akp_instance" "example" {
  name = "argocd"
  // ...
  argocd_secret = {
    "dex.github.clientSecret" = "my-github-oidc-secret"
    "webhook.github.secret"   = "shhhh! it'   s a github secret"
  }
  // ...
  repo_credential_secrets = {
    repo-my-private-https-repo = {
      url                = "https://github.com/argoproj/argocd-example-apps"
      password           = "my-ppassword"
      username           = "my-username"
      insecure           = true
      forceHttpBasicAuth = true
      enableLfs          = true
    },
    repo-my-private-ssh-repo = {
      url           = "ssh://**************/argoproj/argocd-example-apps"
      sshPrivateKey = <<EOF
      # paste the sshPrivateKey data here
      EOF
      insecure      = true
      enableLfs     = true
    }
  }
}
```
Update existing `akp_instance` resource based on the above example for all the mentioned attributes.

## Import the State

After updating your configuration, create a new directory with the updated Terraform configuration file, and run the following command to import the resource states:
```shell
# Import the AKP Instance Resource
terraform import akp_instance.example <instance_name>
# Import the AKP Cluster Resource
terraform import akp_cluster.example <instance_id>/<cluster_name>
```
Verify that the new state file is valid:
```shell
# List the resources in the state file
terraform state list
# Check if the resources are valid
terraform state show akp_instance.example
terraform state show akp_cluster.example
```

## Apply the change

After updating your configuration and import the state, run `terraform plan` again to verify no unintended changes were introduced.
Note that you might notice the `secrets` related attributes are displayed as `changed` in the output of `terraform plan`:
```
Terraform used the selected providers to generate the following execution plan. Resource actions are indicated with the following symbols:
  ~ update in-place

Terraform will perform the following actions:

  # akp_instance.example will be updated in-place
  ~ resource "akp_instance" "example" {
      + argocd_image_updater_secret      = (sensitive value)
      + argocd_notifications_secret      = (sensitive value)
      + argocd_secret                    = (sensitive value)
        id                               = "<id>"
        name                             = "argocd"
      + repo_credential_secrets          = (sensitive value)
      + repo_template_credential_secrets = (sensitive value)
        # (8 unchanged attributes hidden)
    }

Plan: 0 to add, 1 to change, 0 to destroy.
```
This is becuase Akuity Platform API does not return secrets due to security reasons. If you are confident that you haven't modified any values of above `secret` attributes both in the Terraform configuration file and directly on the Akuity Platform, you can safely ignore this and apply the changes.
If you modified existing fields or added new 0.5.0 fields, review the planned changes before running `terraform apply` to apply the updates.