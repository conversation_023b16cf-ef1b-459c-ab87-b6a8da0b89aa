---
page_title: "Terraform AKP Provider version 0.5 upgrading guide"
subcategory: ""
description: Terraform AKP Provider Version 0.5 Upgrade Guide
---
# Terraform AKP Provider Version 0.5 Upgrade Guide
Version 0.5.0 of the AKP provider for Terraform includes changes that you need to consider when upgrading. This guide will help with that process and focuses on changes from version 0.4.x to version 0.5.0.

## Provider Version Configuration

-> Before upgrading to version 0.5.0, upgrade to the most recent 0.4.x version of the provider and ensure that your environment successfully runs [`terraform plan`](https://www.terraform.io/docs/commands/plan.html). You should not see changes you don't expect or deprecation notices. Also, back up your `.tf` and `.tfstate` files prior to starting the upgrade process.

Use [version constraints when configuring Terraform providers](https://www.terraform.io/docs/configuration/providers.html#provider-versions). If you are following that recommendation, update the version constraints in your Terraform configuration and run [`terraform init -upgrade`](https://www.terraform.io/docs/commands/init.html) to download the new version.

For example, given this previous configuration:

```terraform
terraform {
  required_providers {
    akp = {
      source  = "akuity/akp"
      version = "~> 0.4.0"
    }
  }
}

provider "akp" {
  # Configuration options
}
```

Update to the latest 0.5.x version:

```terraform
terraform {
  required_providers {
    akp = {
      source  = "akuity/akp"
      version = "~> 0.5.0"
    }
  }
}

provider "akp" {
  # Configuration options
}
```

## akp_cluster

### Breaking Changes
Version 0.5.0 introduces breaking changes to these fields:

| Field in version 0.4.x      | Field in version 0.5.0                |
|-----------------------------|---------------------------------------|
| `description`               | `spec.description`                    |
| `namespace_scoped`          | `spec.namespace_scoped`               |
| `agent_version (Read Only)` | `spec.data.target_version (Optional)` |
| `auto_upgrade_disabled`     | `spec.data.auto_upgrade_disabled`     |
| `size`                      | `spec.data.size`                      |

Update existing `akp_cluster` resource and data source based on this mapping.

For example, given this version 0.4.x `akp_cluster` resource:

```hcl
resource "akp_cluster" "example" {
  instance_id           = data.akp_instance.example.id
  name                  = "test"
  namespace             = "akuity"
  namespace_scoped      = "true"
  size                  = "small"
  auto_upgrade_disabled = "true"
  description           = "test"
  labels = {
    label1 = "example-label"
  }
  annotations = {
    ann1 = "example-annotation"
  }
}
```

The version 0.5.0 equivalent is:

```hcl
resource "akp_cluster" "example" {
  instance_id = data.akp_instance.example.id
  name        = "test"
  namespace   = "akuity"
  labels = {
    label1 = "example-label"
  }
  annotations = {
    ann1 = "example-annotation"
  }
  spec = {
    namespace_scoped = "true"
    description      = "test"
    data = {
      size                  = "small"
      auto_upgrade_disabled = "true"
    }
  }
}
```

### New Fields
Version 0.5.0 introduces the following new fields on `akp_cluster` resource and data source:

| Field                                 | Description                                                                                                                                                        |
|---------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `spec.data.app_replication (Boolean)` | (Optional) Enables Argo CD state replication to the managed cluster that allows disconnecting the cluster from Akuity Platform without losing core Argocd features |
| `spec.data.kustomization (String)`    | (Optional) Kustomize configuration that will be applied to generated agent installation manifests                                                                  |
| `spec.data.redis_tunneling (Boolean)` | (Optional) Enables the ability to connect to Redis over a web-socket tunnel that allows using Akuity agent behind HTTPS proxy                                      |

You can manage these settings in Terraform by adding the above fields to your `akp_cluster` resource and data source.

## Apply the change
After updating your configuration, run `terraform plan` again to verify no unintended changes were introduced. If you modified existing fields or added new 0.5.0 fields, review the planned changes before running `terraform apply` to apply the updates.