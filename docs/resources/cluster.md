---
# generated by https://github.com/hashicorp/terraform-plugin-docs
page_title: "akp_cluster Resource - akp"
subcategory: ""
description: |-
  Manages a cluster attached to an Argo CD instance.
---

# akp_cluster (Resource)

Manages a cluster attached to an Argo CD instance.

## Example Usage (Basic)
```terraform
resource "akp_cluster" "my-cluster" {
  instance_id = akp_instance.argocd.id
  kube_config = {
    host                   = "https://${cluster.my-cluster.endpoint}"
    token                  = var.my_token
    client_certificate     = "${base64decode(cluster.my-cluster.master_auth.0.client_certificate)}"
    client_key             = "${base64decode(cluster.my-cluster.master_auth.0.client_key)}"
    cluster_ca_certificate = "${base64decode(cluster.my-cluster.master_auth.0.cluster_ca_certificate)}"
  }
  name      = "my-cluster"
  namespace = "akuity"
  spec = {
    data = {
      size = "small"
    }
  }
}
```

- The `instance_id` assumes you have an `akp_instance` resource named `argocd`.
- The `kube_config` assumes you have a resource named `cluster.my-cluster` that provides the `host`, `token`, `client_certificate`, `client_key`, `cluster_ca_certificate` of the Kubernetes cluster to deploy the agent into.

For a complete working example using a GKE cluster, see [akuity/examples](https://github.com/akuity/examples/tree/main/terraform/akuity).

## Example Usage (Custom agent size)
```terraform
data "akp_instance" "example" {
  name = "test"
}

resource "akp_cluster" "example" {
  instance_id = data.akp_instance.example.id
  name        = "test-cluster"
  namespace   = "test"
  labels = {
    test-label = true
  }
  annotations = {
    test-annotation = false
  }
  spec = {
    namespace_scoped = true
    description      = "test-description"
    data = {
      size                  = "custom"
      auto_upgrade_disabled = false
      custom_agent_size_config = {
        application_controller = {
          cpu    = "1000m"
          memory = "2Gi"
        }
        repo_server = {
          replicas = 3,
          cpu      = "1000m"
          memory   = "2Gi"
        }
      }
    }
  }
}
```

## Example Usage (Auto agent size)
```terraform
data "akp_instance" "example" {
  name = "test"
}

resource "akp_cluster" "example" {
  instance_id = data.akp_instance.example.id
  name        = "test-cluster"
  namespace   = "test"
  labels = {
    test-label = true
  }
  annotations = {
    test-annotation = false
  }
  spec = {
    namespace_scoped = true
    description      = "test-description"
    data = {
      size = "auto"
      # auto_upgrade_disabled  can be set to true if you want to enable auto scaling of the agent size for the cluster
      auto_upgrade_disabled = false
      auto_agent_size_config = {
        application_controller = {
          resource_maximum = {
            cpu    = "3"
            memory = "2Gi"
          },
          resource_minimum = {
            cpu    = "250m",
            memory = "1Gi"
          }
        },
        repo_server = {
          replicas_maximum = 3,
          # minimum number of replicas should be set to 1
          replicas_minimum = 1,
          resource_maximum = {
            cpu    = "3"
            memory = "2.00Gi"
          },
          resource_minimum = {
            cpu    = "250m",
            memory = "256Mi"
          }
        }
      }
    }
  }
}
```

- This example uses the `auto` agent size, which will automatically scale the agent based on the number of applications in the cluster. `auto_upgrade_disabled` cannot be set to `true` when using `auto` agent size.

## Example Usage (Exhaustive)
```terraform
data "akp_instance" "example" {
  name = "test"
}

resource "akp_cluster" "example" {
  instance_id = data.akp_instance.example.id
  name        = "test-cluster"
  namespace   = "test"
  labels = {
    test-label = true
  }
  annotations = {
    test-annotation = false
  }
  spec = {
    namespace_scoped = true
    description      = "test-description"
    data = {
      size                  = "small"
      auto_upgrade_disabled = true
      target_version        = "0.4.0"
      managed_cluster_config = {
        secret_key  = "secret"
        secret_name = "secret-name"
      }
      eks_addon_enabled           = true
      datadog_annotations_enabled = true
      kustomization               = <<EOF
  apiVersion: kustomize.config.k8s.io/v1beta1
  kind: Kustomization
  patches:
    - patch: |-
        apiVersion: apps/v1
        kind: Deployment
        metadata:
          name: argocd-repo-server
        spec:
          template:
            spec:
              containers:
              - name: argocd-repo-server
                resources:
                  limits:
                    memory: 2Gi
                  requests:
                    cpu: 750m
                    memory: 1Gi
      target:
        kind: Deployment
        name: argocd-repo-server
            EOF
    }
  }

  kube_config = {
    config_path = "test.kubeconfig"
    token       = "YOUR TOKEN"
  }

  # When using a Kubernetes token retrieved from a Terraform provider (e.g. aws_eks_cluster_auth or google_client_config) in the above `kube_config`,
  # the token value may change over time. This will cause Terraform to detect a diff in the `token` on each plan and apply.
  # To prevent constant changes, you can add the `token` field path to the `lifecycle` block's `ignore_changes` list:
  #  https://developer.hashicorp.com/terraform/language/meta-arguments/lifecycle#ignore_changes
  lifecycle {
    ignore_changes = [
      kube_config.token,
    ]
  }
}
```

<!-- schema generated by tfplugindocs -->
## Schema

### Required

- `instance_id` (String) Argo CD instance ID
- `name` (String) Cluster name
- `namespace` (String) Agent installation namespace
- `spec` (Attributes) Cluster spec (see [below for nested schema](#nestedatt--spec))

### Optional

- `annotations` (Map of String) Annotations
- `kube_config` (Attributes) Kubernetes connection settings. If configured, terraform will try to connect to the cluster and install the agent (see [below for nested schema](#nestedatt--kube_config))
- `labels` (Map of String) Labels
- `remove_agent_resources_on_destroy` (Boolean) Remove agent Kubernetes resources from the managed cluster when destroying cluster, default to `true`

### Read-Only

- `id` (String) Cluster ID

<a id="nestedatt--spec"></a>
### Nested Schema for `spec`

Required:

- `data` (Attributes) Cluster data (see [below for nested schema](#nestedatt--spec--data))

Optional:

- `description` (String) Cluster description
- `namespace_scoped` (Boolean) If the agent is namespace scoped

<a id="nestedatt--spec--data"></a>
### Nested Schema for `spec.data`

Required:

- `size` (String) Cluster Size. One of `small`, `medium`, `large`, `custom` or `auto`

Optional:

- `app_replication` (Boolean) Enables Argo CD state replication to the managed cluster that allows disconnecting the cluster from Akuity Platform without losing core Argocd features
- `auto_agent_size_config` (Attributes) Autoscaler config for auto agent size (see [below for nested schema](#nestedatt--spec--data--auto_agent_size_config))
- `auto_upgrade_disabled` (Boolean) Disable Agents Auto Upgrade. On resource update terraform will try to update the agent if this is set to `true`. Otherwise agent will update itself automatically
- `custom_agent_size_config` (Attributes) Custom agent size config (see [below for nested schema](#nestedatt--spec--data--custom_agent_size_config))
- `datadog_annotations_enabled` (Boolean) Enable Datadog metrics collection of Application Controller and Repo Server. Make sure that you install Datadog agent in cluster.
- `eks_addon_enabled` (Boolean) Enable this if you are installing this cluster on EKS.
- `kustomization` (String) Kustomize configuration that will be applied to generated agent installation manifests
- `managed_cluster_config` (Attributes) The config to access managed Kubernetes cluster. By default agent is using "in-cluster" config. (see [below for nested schema](#nestedatt--spec--data--managed_cluster_config))
- `multi_cluster_k8s_dashboard_enabled` (Boolean) Enable the KubeVision feature on the managed cluster
- `project` (String) Project name
- `redis_tunneling` (Boolean) Enables the ability to connect to Redis over a web-socket tunnel that allows using Akuity agent behind HTTPS proxy
- `target_version` (String) The version of the agent to install on your cluster

<a id="nestedatt--spec--data--auto_agent_size_config"></a>
### Nested Schema for `spec.data.auto_agent_size_config`

Optional:

- `application_controller` (Attributes) Application Controller auto scaling config (see [below for nested schema](#nestedatt--spec--data--auto_agent_size_config--application_controller))
- `repo_server` (Attributes) Repo Server auto scaling config (see [below for nested schema](#nestedatt--spec--data--auto_agent_size_config--repo_server))

<a id="nestedatt--spec--data--auto_agent_size_config--application_controller"></a>
### Nested Schema for `spec.data.auto_agent_size_config.application_controller`

Optional:

- `resource_maximum` (Attributes) Resource maximum (see [below for nested schema](#nestedatt--spec--data--auto_agent_size_config--application_controller--resource_maximum))
- `resource_minimum` (Attributes) Resource minimum (see [below for nested schema](#nestedatt--spec--data--auto_agent_size_config--application_controller--resource_minimum))

<a id="nestedatt--spec--data--auto_agent_size_config--application_controller--resource_maximum"></a>
### Nested Schema for `spec.data.auto_agent_size_config.application_controller.resource_maximum`

Optional:

- `cpu` (String) CPU
- `memory` (String) Memory


<a id="nestedatt--spec--data--auto_agent_size_config--application_controller--resource_minimum"></a>
### Nested Schema for `spec.data.auto_agent_size_config.application_controller.resource_minimum`

Optional:

- `cpu` (String) CPU
- `memory` (String) Memory



<a id="nestedatt--spec--data--auto_agent_size_config--repo_server"></a>
### Nested Schema for `spec.data.auto_agent_size_config.repo_server`

Optional:

- `replicas_maximum` (Number) Replica maximum
- `replicas_minimum` (Number) Replica minimum, this should be set to 1 as a minimum
- `resource_maximum` (Attributes) Resource maximum (see [below for nested schema](#nestedatt--spec--data--auto_agent_size_config--repo_server--resource_maximum))
- `resource_minimum` (Attributes) Resource minimum (see [below for nested schema](#nestedatt--spec--data--auto_agent_size_config--repo_server--resource_minimum))

<a id="nestedatt--spec--data--auto_agent_size_config--repo_server--resource_maximum"></a>
### Nested Schema for `spec.data.auto_agent_size_config.repo_server.resource_maximum`

Optional:

- `cpu` (String) CPU
- `memory` (String) Memory


<a id="nestedatt--spec--data--auto_agent_size_config--repo_server--resource_minimum"></a>
### Nested Schema for `spec.data.auto_agent_size_config.repo_server.resource_minimum`

Optional:

- `cpu` (String) CPU
- `memory` (String) Memory




<a id="nestedatt--spec--data--custom_agent_size_config"></a>
### Nested Schema for `spec.data.custom_agent_size_config`

Optional:

- `application_controller` (Attributes) Application Controller custom agent size config (see [below for nested schema](#nestedatt--spec--data--custom_agent_size_config--application_controller))
- `repo_server` (Attributes) Repo Server custom agent size config (see [below for nested schema](#nestedatt--spec--data--custom_agent_size_config--repo_server))

<a id="nestedatt--spec--data--custom_agent_size_config--application_controller"></a>
### Nested Schema for `spec.data.custom_agent_size_config.application_controller`

Optional:

- `cpu` (String) CPU
- `memory` (String) Memory


<a id="nestedatt--spec--data--custom_agent_size_config--repo_server"></a>
### Nested Schema for `spec.data.custom_agent_size_config.repo_server`

Optional:

- `cpu` (String) CPU
- `memory` (String) Memory
- `replicas` (Number) Replica



<a id="nestedatt--spec--data--managed_cluster_config"></a>
### Nested Schema for `spec.data.managed_cluster_config`

Required:

- `secret_name` (String) The name of the secret for the managed cluster config

Optional:

- `secret_key` (String) The key in the secret for the managed cluster config




<a id="nestedatt--kube_config"></a>
### Nested Schema for `kube_config`

Optional:

- `client_certificate` (String) PEM-encoded client certificate for TLS authentication.
- `client_key` (String, Sensitive) PEM-encoded client certificate key for TLS authentication.
- `cluster_ca_certificate` (String) PEM-encoded root certificates bundle for TLS authentication.
- `config_context` (String) Context name to load from the kube config file.
- `config_context_auth_info` (String)
- `config_context_cluster` (String)
- `config_path` (String) Path to the kube config file.
- `config_paths` (List of String) A list of paths to kube config files.
- `host` (String) The hostname (in form of URI) of Kubernetes master.
- `insecure` (Boolean) Whether server should be accessed without verifying the TLS certificate.
- `password` (String, Sensitive) The password to use for HTTP basic authentication when accessing the Kubernetes master endpoint.
- `proxy_url` (String) URL to the proxy to be used for all API requests
- `token` (String, Sensitive) Token to authenticate an service account
- `username` (String) The username to use for HTTP basic authentication when accessing the Kubernetes master endpoint.

## Import

In Terraform v1.5.0 and later, use an [`import` block](https://developer.hashicorp.com/terraform/language/import) to import the AKP cluster using `instance_id` and `name` separated by a forward slash (`/`). For example:

```terraform
import {
  to = akp_cluster.example
  id = "6pzhawvy4echbd8x/test-cluster"
}
```

Using `terraform import`, import AKP cluster using `instance_id` and `name` separated by a forward slash (`/`). For example:

```shell
terraform import akp_cluster.example 6pzhawvy4echbd8x/test-cluster
```
