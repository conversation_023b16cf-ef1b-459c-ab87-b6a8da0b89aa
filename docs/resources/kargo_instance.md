---
# generated by https://github.com/hashicorp/terraform-plugin-docs
page_title: "akp_kargo_instance Resource - akp"
subcategory: ""
description: |-
  Manages an AKP Kargo instance.
---

# akp_kargo_instance (Resource)

Manages an AKP Kargo instance.

## Example Usage (Basic)
```terraform
resource "akp_kargo_instance" "example" {
  name = "test"
  kargo = {
    spec = {
      version             = "v1.1.1"
      kargo_instance_spec = {}
    }
  }
}
```

## Example Usage (Exhaustive)
```terraform
resource "akp_kargo_instance" "example" {
  name      = "test"
  workspace = "kargo-workspace"
  kargo_cm = {
    adminAccountEnabled  = "true"
    adminAccountTokenTtl = "24h"
  }
  kargo_secret = {
    adminAccountPasswordHash = "$2a$10$wThs/VVwx5Tbygkk5Rzbv.V8hR8JYYmRdBiGjue9pd0YcEXl7.Kn."
  }
  kargo = {
    spec = {
      description = "test-description"
      version     = "v1.4.3"
      // only set one of fqdn and subdomain
      fqdn      = "fqdn.example.com"
      subdomain = ""
      oidc_config = {
        enabled     = true
        dex_enabled = false
        # client_id should be set only if dex_enabled is false
        client_id = "test-client-id"
        # client_secret should be set only if dex_enabled is false
        cli_client_id = "test-cli-client-id"
        # issuer_url should be set only if dex_enabled is false
        issuer_url = "https://test.com"
        # additional_scopes should be set only if dex_enabled is false
        additional_scopes = ["test-scope"]
        # dex_secret should be set only if dex_enabled is false
        dex_secret = {
          name = "test-secret"
        }
        # dex_config should be set only if dex_enabled is true, and if dex is set, then oidc related fields should not be set
        dex_config = ""
        admin_account = {
          claims = {
            groups = {
              values = ["<EMAIL>"]
            }
            email = {
              values = ["<EMAIL>"]
            }
            sub = {
              values = ["<EMAIL>"]
            }
          }
        }
        viewer_account = {
          claims = {
            groups = {
              values = ["<EMAIL>"]
            }
            email = {
              values = ["<EMAIL>"]
            }
            sub = {
              values = ["<EMAIL>"]
            }
          }
        }
      }
      kargo_instance_spec = {
        backend_ip_allow_list_enabled = true
        ip_allow_list = [
          {
            ip          = "***********"
            description = "test-description"
          }
        ]
        agent_customization_defaults = {
          auto_upgrade_disabled = true
          kustomization         = <<-EOT
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
images:
  - name: ghcr.io/akuity/kargo
    newName: quay.io/akuityy/kargo
  - name: quay.io/akuityio/argo-rollouts
    newName: quay.io/akuityy/argo-rollouts
  - name: quay.io/akuity/agent
    newName: quay.io/akuityy/agent
EOT
        }
        default_shard_agent       = "test"
        global_credentials_ns     = ["test1", "test2"]
        global_service_account_ns = ["test3", "test4"]
      }
    }
  }
  kargo_resources = local.kargo_resources
}

# Choose a directory that contains Kargo resource manifests.
# For example, here we have kargo.yaml in the kargo-manifests directory, and the data is like:
# ---------------------------------------------
# apiVersion: kargo.akuity.io/v1alpha1
# kind: Project
# metadata:
#   name: kargo-demo
# ---
# apiVersion: kargo.akuity.io/v1alpha1
# kind: Warehouse
# metadata:
#   name: kargo-demo
#   namespace: kargo-demo
# spec:
#   subscriptions:
#   - image:
#       repoURL: public.ecr.aws/nginx/nginx
#       semverConstraint: ^1.28.0
#       discoveryLimit: 5
# ---
# ...
# ---------------------------------------------
#
# The following expression can parse the provided YAMLs into JSON strings for the provider to be validated and applied correctly.
# Remember to put the parsed kargo resources into `akp_kargo_instance.kargo_resources` field.
locals {
  yaml_files = fileset("${path.module}/kargo-manifests", "*.yaml")

  kargo_resources = flatten([
    for file_name in local.yaml_files : [
      for resource in split("\n---\n", file("${path.module}/kargo-manifests/${file_name}")) :
      jsonencode(yamldecode(resource))
    ]
  ])
}
```

<!-- schema generated by tfplugindocs -->
## Schema

### Required

- `kargo` (Attributes) Kargo instance configuration (see [below for nested schema](#nestedatt--kargo))
- `name` (String) Kargo Instance name

### Optional

- `kargo_cm` (Map of String) ConfigMap to configure system account accesses. The usage can be found in the examples/resources/akp_kargo_instance/resource.tf
- `kargo_resources` (List of String) List of Kargo custom resources to be managed alongside the Kargo instance. Currently supported resources are: `Project`, `ClusterPromotionTask`, `Stage`, `Warehouse`, `AnalysisTemplate`, `PromotionTask`. Should all be in the apiVersion `kargo.akuity.io/v1alpha1`.
- `kargo_secret` (Map of String, Sensitive) Secret to configure system account accesses. The usage can be found in the examples/resources/akp_kargo_instance/resource.tf
- `workspace` (String) Workspace name for the Kargo instance

### Read-Only

- `id` (String) Kargo Instance ID

<a id="nestedatt--kargo"></a>
### Nested Schema for `kargo`

Required:

- `spec` (Attributes) Kargo instance spec (see [below for nested schema](#nestedatt--kargo--spec))

<a id="nestedatt--kargo--spec"></a>
### Nested Schema for `kargo.spec`

Required:

- `kargo_instance_spec` (Attributes) Kargo instance spec (see [below for nested schema](#nestedatt--kargo--spec--kargo_instance_spec))
- `version` (String) Version of the Kargo instance

Optional:

- `description` (String) Description of the Kargo instance
- `fqdn` (String) Fully qualified domain name
- `oidc_config` (Attributes) OIDC configuration (see [below for nested schema](#nestedatt--kargo--spec--oidc_config))
- `subdomain` (String) Subdomain of the Kargo instance

<a id="nestedatt--kargo--spec--kargo_instance_spec"></a>
### Nested Schema for `kargo.spec.kargo_instance_spec`

Optional:

- `agent_customization_defaults` (Attributes) Default agent customization settings (see [below for nested schema](#nestedatt--kargo--spec--kargo_instance_spec--agent_customization_defaults))
- `backend_ip_allow_list_enabled` (Boolean) Whether IP allow list is enabled for the backend
- `default_shard_agent` (String) Default shard agent
- `global_credentials_ns` (List of String) List of global credentials namespaces
- `global_service_account_ns` (List of String) List of global service account namespaces
- `ip_allow_list` (Attributes List) List of allowed IPs (see [below for nested schema](#nestedatt--kargo--spec--kargo_instance_spec--ip_allow_list))

<a id="nestedatt--kargo--spec--kargo_instance_spec--agent_customization_defaults"></a>
### Nested Schema for `kargo.spec.kargo_instance_spec.agent_customization_defaults`

Optional:

- `auto_upgrade_disabled` (Boolean) Whether auto upgrade is disabled
- `kustomization` (String) Kustomization that will be applied to the Kargo agent to generate agent installation manifests


<a id="nestedatt--kargo--spec--kargo_instance_spec--ip_allow_list"></a>
### Nested Schema for `kargo.spec.kargo_instance_spec.ip_allow_list`

Required:

- `ip` (String) IP Address

Optional:

- `description` (String) Description



<a id="nestedatt--kargo--spec--oidc_config"></a>
### Nested Schema for `kargo.spec.oidc_config`

Optional:

- `additional_scopes` (List of String) Additional scopes
- `admin_account` (Attributes) Admin account (see [below for nested schema](#nestedatt--kargo--spec--oidc_config--admin_account))
- `cli_client_id` (String) CLI Client ID
- `client_id` (String) Client ID
- `dex_config` (String) DEX configuration
- `dex_config_secret` (Map of String) DEX configuration secret
- `dex_enabled` (Boolean) Whether DEX is enabled
- `enabled` (Boolean) Whether OIDC is enabled
- `issuer_url` (String) Issuer URL
- `viewer_account` (Attributes) Viewer account (see [below for nested schema](#nestedatt--kargo--spec--oidc_config--viewer_account))

<a id="nestedatt--kargo--spec--oidc_config--admin_account"></a>
### Nested Schema for `kargo.spec.oidc_config.admin_account`

Optional:

- `claims` (Attributes Map) Claims (see [below for nested schema](#nestedatt--kargo--spec--oidc_config--admin_account--claims))

<a id="nestedatt--kargo--spec--oidc_config--admin_account--claims"></a>
### Nested Schema for `kargo.spec.oidc_config.admin_account.claims`

Optional:

- `values` (List of String)



<a id="nestedatt--kargo--spec--oidc_config--viewer_account"></a>
### Nested Schema for `kargo.spec.oidc_config.viewer_account`

Optional:

- `claims` (Attributes Map) Claims (see [below for nested schema](#nestedatt--kargo--spec--oidc_config--viewer_account--claims))

<a id="nestedatt--kargo--spec--oidc_config--viewer_account--claims"></a>
### Nested Schema for `kargo.spec.oidc_config.viewer_account.claims`

Optional:

- `values` (List of String)

## Import

In Terraform v1.5.0 and later, use an [`import` block](https://developer.hashicorp.com/terraform/language/import) to import the AKP Kargo instance using its `name`. For example:

```terraform
import {
  to = akp_kargo_instance.example
  id = "test"
}
```

Using `terraform import`, import AKP Kargo instance using its `name`. For example:

```shell
terraform import akp_kargo_instance.example test
```
