---
# generated by https://github.com/hashicorp/terraform-plugin-docs
page_title: "akp_instance Resource - akp"
subcategory: ""
description: |-
  Manages an Argo CD instance
---

# akp_instance (Resource)

Manages an Argo CD instance

## Example Usage (Basic)
```terraform
resource "akp_instance" "argocd" {
  name = "argocd"
  argocd = {
    "spec" = {
      "instance_spec" = {
        "declarative_management_enabled" = true
      }
      "version" = "v2.11.4"
    }
  }
}
```

## Example Usage (Config Management Plugins)

Please read the [Managing Config Management Plugins Guide](../guides/managing-cmps.md) before you start.

```terraform
resource "akp_instance" "argocd" {
  name = "argocd"
  argocd = {
    "spec" = {
      "instance_spec" = {
        "declarative_management_enabled" = true
      }
      "version" = "v2.11.4"
    }
  }
  config_management_plugins = {
    "kasane" = {
      image   = "gcr.io/kasaneapp/kasane"
      enabled = false
      spec = {
        init = {
          command = [
            "kasane",
            "update"
          ]
        }
        generate = {
          command = [
            "kasane",
            "show"
          ]
        }
      }
    }
    "tanka" = {
      enabled = true
      image   = "grafana/tanka:0.25.0"
      spec = {
        discover = {
          file_name = "jsonnetfile.json"
        }
        generate = {
          args = [
            "tk show environments/$PARAM_ENV --dangerous-allow-redirect",
          ]
          command = [
            "sh",
            "-c",
          ]
        }
        init = {
          command = [
            "jb",
            "update",
          ]
        }
        parameters = {
          static = [
            {
              name     = "env"
              required = true
              string   = "default"
            },
          ]
        }
        preserve_file_mode = false
        version            = "v1.0"
      }
    },
  }
}
```

## Example Usage (Exhaustive)
```terraform
resource "akp_instance" "example" {
  name = "test"
  argocd = {
    spec = {
      description = "test-inst"
      instance_spec = {
        declarative_management_enabled = false
        backend_ip_allow_list_enabled  = true
        image_updater_enabled          = true
        ip_allow_list = [
          {
            description = "dummy entry2"
            ip          = "*******"
          },
        ]
        cluster_customization_defaults = {
          auto_upgrade_disabled = true
        }
        appset_policy = {
          policy          = "create-only"
          override_policy = true
        }
        host_aliases = [
          {
            hostnames = ["test.example.com"]
            ip        = "*******"
          },
        ]
        crossplane_extension = {
          resources = [
            {
              group = "*.example.crossplane.*",
            }
          ]
        }
        agent_permissions_rules = [
          {
            api_groups = ["batch"]
            resources  = ["jobs"]
            verbs      = ["create"]
          }
        ]
        fqdn = "test.example.com"
        appset_plugins = [
          {
            # name needs to start with plugin-
            name = "plugin-test"
            # the secret that refers to
            token           = "$application-set-secret:token"
            base_url        = "https://example.com"
            request_timeout = 30
          }
        ]
        app_in_any_namespace_config = {
          enabled = true
        }
      }
      version = "v2.11.4"
    }
  }
  argocd_cm = {
    # When configuring `argocd_cm`, there is generally no need to set all of these keys. If you do not set a key, the API will set suitable default values.
    # Please note that the API will disallow the setting of  any key which isn't a known configuration option in `argocd-cm`.
    #
    # NOTE:
    # `admin.enabled` can be set to `false` to disable the admin login.
    # To enable the admin account, set `accounts.admin: "login"`, and to disable the admin login, set `admin.enabled: false`. They are mutually exclusive.
    "exec.enabled"                   = true
    "ga.anonymizeusers"              = false
    "helm.enabled"                   = true
    "kustomize.enabled"              = true
    "server.rbac.log.enforce.enable" = false
    "statusbadge.enabled"            = false
    "ui.bannerpermanent"             = false
    "users.anonymous.enabled"        = true

    "kustomize.buildOptions" = "--load_restrictor none"
    "accounts.alice"         = "login"
    "dex.config"             = <<-EOF
        connectors:
          # GitHub example
          - type: github
            id: github
            name: GitHub
            config:
              clientID: aabbccddeeff00112233
              clientSecret: $dex.github.clientSecret
              orgs:
              - name: your-github-org
        EOF
  }
  argocd_rbac_cm = {
    "policy.default" = "role:readonly"
    "policy.csv"     = <<-EOF
         p, role:org-admin, applications, *, */*, allow
         p, role:org-admin, clusters, get, *, allow
         g, your-github-org:your-team, role:org-admin
         EOF
  }
  argocd_secret = {
    "dex.github.clientSecret" = "my-github-oidc-secret"
    "webhook.github.secret"   = "shhhh! it'   s a github secret"
  }
  application_set_secret = {
    "my-appset-secret" = "xyz456"
  }
  argocd_notifications_cm = {
    "trigger.on-sync-status-unknown" = <<-EOF
        - when: app.status.sync.status == 'Unknown'
          send: [my-custom-template]
      EOF
    "template.my-custom-template"    = <<-EOF
        message: |
          Application details: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}.
      EOF
    "defaultTriggers"                = <<-EOF
        - on-sync-status-unknown
      EOF
  }
  argocd_notifications_secret = {
    email-username = "<EMAIL>"
    email-password = "password"
  }
  argocd_image_updater_ssh_config = {
    config = <<-EOF
      Host *
            PubkeyAcceptedAlgorithms +ssh-rsa
            HostkeyAlgorithms +ssh-rsa
            HostkeyAlgorithms2 +ssh-rsa
    EOF
  }
  argocd_image_updater_config = {
    "registries.conf" = <<-EOF
      registries:
        - prefix: docker.io
          name: Docker2
          api_url: https://registry-1.docker.io
          credentials: secret:argocd/argocd-image-updater-secret#my-docker-credentials
    EOF
    "git.email"       = "<EMAIL>"
    "git.user"        = "akuitybot"
  }
  argocd_image_updater_secret = {
    my-docker-credentials = "abcd1234"
  }
  argocd_ssh_known_hosts_cm = {
    # When configuring the known host list, make sure to add the following default ones before adding your own hosts.
    ssh_known_hosts = <<EOF
[ssh.github.com]:443 ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBEmKSENjQEezOmxkZMy7opKgwFB9nkt5YRrYMjNuG5N87uRgg6CLrbo5wAdT/y6v0mKV0U2w0WZ2YB/++Tpockg=
[ssh.github.com]:443 ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOMqqnkVzrm0SdG6UOoqKLsabgH5C9okWi0dh2l9GKJl
[ssh.github.com]:443 ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCj7ndNxQowgcQnjshcLrqPEiiphnt+VTTvDP6mHBL9j1aNUkY4Ue1gvwnGLVlOhGeYrnZaMgRK6+PKCUXaDbC7qtbW8gIkhL7aGCsOr/C56SJMy/BCZfxd1nWzAOxSDPgVsmerOBYfNqltV9/hWCqBywINIR+5dIg6JTJ72pcEpEjcYgXkE2YEFXV1JHnsKgbLWNlhScqb2UmyRkQyytRLtL+38TGxkxCflmO+5Z8CSSNY7GidjMIZ7Q4zMjA2n1nGrlTDkzwDCsw+wqFPGQA179cnfGWOWRVruj16z6XyvxvjJwbz0wQZ75XK5tKSb7FNyeIEs4TT4jk+S4dhPeAUC5y+bDYirYgM4GC7uEnztnZyaVWQ7B381AK4Qdrwt51ZqExKbQpTUNn+EjqoTwvqNj4kqx5QUCI0ThS/YkOxJCXmPUWZbhjpCg56i+2aB6CmK2JGhn57K5mj0MNdBXA4/WnwH6XoPWJzK5Nyu2zB3nAZp+S5hpQs+p1vN1/wsjk=
bitbucket.org ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBPIQmuzMBuKdWeF4+a2sjSSpBK0iqitSQ+5BM9KhpexuGt20JpTVM7u5BDZngncgrqDMbWdxMWWOGtZ9UgbqgZE=
bitbucket.org ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIIazEu89wgQZ4bqs3d63QSMzYVa0MuJ2e2gKTKqu+UUO
bitbucket.org ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDQeJzhupRu0u0cdegZIa8e86EG2qOCsIsD1Xw0xSeiPDlCr7kq97NLmMbpKTX6Esc30NuoqEEHCuc7yWtwp8dI76EEEB1VqY9QJq6vk+aySyboD5QF61I/1WeTwu+deCbgKMGbUijeXhtfbxSxm6JwGrXrhBdofTsbKRUsrN1WoNgUa8uqN1Vx6WAJw1JHPhglEGGHea6QICwJOAr/6mrui/oB7pkaWKHj3z7d1IC4KWLtY47elvjbaTlkN04Kc/5LFEirorGYVbt15kAUlqGM65pk6ZBxtaO3+30LVlORZkxOh+LKL/BvbZ/iRNhItLqNyieoQj/uh/7Iv4uyH/cV/0b4WDSd3DptigWq84lJubb9t/DnZlrJazxyDCulTmKdOR7vs9gMTo+uoIrPSb8ScTtvw65+odKAlBj59dhnVp9zd7QUojOpXlL62Aw56U4oO+FALuevvMjiWeavKhJqlR7i5n9srYcrNV7ttmDw7kf/97P5zauIhxcjX+xHv4M=
github.com ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBEmKSENjQEezOmxkZMy7opKgwFB9nkt5YRrYMjNuG5N87uRgg6CLrbo5wAdT/y6v0mKV0U2w0WZ2YB/++Tpockg=
github.com ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOMqqnkVzrm0SdG6UOoqKLsabgH5C9okWi0dh2l9GKJl
github.com ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCj7ndNxQowgcQnjshcLrqPEiiphnt+VTTvDP6mHBL9j1aNUkY4Ue1gvwnGLVlOhGeYrnZaMgRK6+PKCUXaDbC7qtbW8gIkhL7aGCsOr/C56SJMy/BCZfxd1nWzAOxSDPgVsmerOBYfNqltV9/hWCqBywINIR+5dIg6JTJ72pcEpEjcYgXkE2YEFXV1JHnsKgbLWNlhScqb2UmyRkQyytRLtL+38TGxkxCflmO+5Z8CSSNY7GidjMIZ7Q4zMjA2n1nGrlTDkzwDCsw+wqFPGQA179cnfGWOWRVruj16z6XyvxvjJwbz0wQZ75XK5tKSb7FNyeIEs4TT4jk+S4dhPeAUC5y+bDYirYgM4GC7uEnztnZyaVWQ7B381AK4Qdrwt51ZqExKbQpTUNn+EjqoTwvqNj4kqx5QUCI0ThS/YkOxJCXmPUWZbhjpCg56i+2aB6CmK2JGhn57K5mj0MNdBXA4/WnwH6XoPWJzK5Nyu2zB3nAZp+S5hpQs+p1vN1/wsjk=
gitlab.com ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBFSMqzJeV9rUzU4kWitGjeR4PWSa29SPqJ1fVkhtj3Hw9xjLVXVYrU9QlYWrOLXBpQ6KWjbjTDTdDkoohFzgbEY=
gitlab.com ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAfuCHKVTjquxvt6CM6tdG4SLp1Btn/nOeHHE5UOzRdf
gitlab.com ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCsj2bNKTBSpIYDEGk9KxsGh3mySTRgMtXL583qmBpzeQ+jqCMRgBqB98u3z++J1sKlXHWfM9dyhSevkMwSbhoR8XIq/U0tCNyokEi/ueaBMCvbcTHhO7FcwzY92WK4Yt0aGROY5qX2UKSeOvuP4D6TPqKF1onrSzH9bx9XUf2lEdWT/ia1NEKjunUqu1xOB/StKDHMoX4/OKyIzuS0q/T1zOATthvasJFoPrAjkohTyaDUz2LN5JoH839hViyEG82yB+MjcFV5MU3N1l1QL3cVUCh93xSaua1N85qivl+siMkPGbO5xR/En4iEY6K2XPASUEMaieWVNTRCtJ4S8H+9
ssh.dev.azure.com ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC7Hr1oTWqNqOlzGJOfGJ4NakVyIzf1rXYd4d7wo6jBlkLvCA4odBlL0mDUyZ0/QUfTTqeu+tm22gOsv+VrVTMk6vwRU75gY/y9ut5Mb3bR5BV58dKXyq9A9UeB5Cakehn5Zgm6x1mKoVyf+FFn26iYqXJRgzIZZcZ5V6hrE0Qg39kZm4az48o0AUbf6Sp4SLdvnuMa2sVNwHBboS7EJkm57XQPVU3/QpyNLHbWDdzwtrlS+ez30S3AdYhLKEOxAG8weOnyrtLJAUen9mTkol8oII1edf7mWWbWVf0nBmly21+nZcmCTISQBtdcyPaEno7fFQMDD26/s0lfKob4Kw8H
vs-ssh.visualstudio.com ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC7Hr1oTWqNqOlzGJOfGJ4NakVyIzf1rXYd4d7wo6jBlkLvCA4odBlL0mDUyZ0/QUfTTqeu+tm22gOsv+VrVTMk6vwRU75gY/y9ut5Mb3bR5BV58dKXyq9A9UeB5Cakehn5Zgm6x1mKoVyf+FFn26iYqXJRgzIZZcZ5V6hrE0Qg39kZm4az48o0AUbf6Sp4SLdvnuMa2sVNwHBboS7EJkm57XQPVU3/QpyNLHbWDdzwtrlS+ez30S3AdYhLKEOxAG8weOnyrtLJAUen9mTkol8oII1edf7mWWbWVf0nBmly21+nZcmCTISQBtdcyPaEno7fFQMDD26/s0lfKob4Kw8H
      EOF
  }
  argocd_tls_certs_cm = {
    "server.example.com" = <<EOF
          -----BEGIN CERTIFICATE-----
          ......
          -----END CERTIFICATE-----
      EOF
  }
  repo_credential_secrets = {
    repo-my-private-https-repo = {
      url                = "https://github.com/argoproj/argocd-example-apps"
      password           = "my-ppassword"
      username           = "my-username"
      insecure           = true
      forceHttpBasicAuth = true
      enableLfs          = true
    },
    repo-my-private-ssh-repo = {
      url           = "ssh://**************/argoproj/argocd-example-apps"
      sshPrivateKey = <<EOF
      # paste the sshPrivateKey data here
      EOF
      insecure      = true
      enableLfs     = true
    }
  }
  repo_template_credential_secrets = {
    repo-argoproj-https-creds = {
      url      = "https://github.com/argoproj"
      type     = "helm"
      password = "my-password"
      username = "my-username"
    }
  }
  config_management_plugins = {
    "kasane" = {
      image   = "gcr.io/kasaneapp/kasane"
      enabled = true
      spec = {
        init = {
          command = [
            "kasane",
            "update"
          ]
        }
        generate = {
          command = [
            "kasane",
            "show"
          ]
        }
      }
    }
    "tanka" = {
      enabled = true
      image   = "grafana/tanka:0.25.0"
      spec = {
        discover = {
          file_name = "jsonnetfile.json"
        }
        generate = {
          args = [
            "tk show environments/$PARAM_ENV --dangerous-allow-redirect",
          ]
          command = [
            "sh",
            "-c",
          ]
        }
        init = {
          command = [
            "jb",
            "update",
          ]
        }
        parameters = {
          static = [
            {
              name     = "env"
              required = true
              string   = "default"
            },
          ]
        }
        preserve_file_mode = false
        version            = "v1.0"
      }
    },
  }
}
```

<!-- schema generated by tfplugindocs -->
## Schema

### Required

- `argocd` (Attributes) Argo CD instance configuration (see [below for nested schema](#nestedatt--argocd))
- `name` (String) Instance name

### Optional

- `application_set_secret` (Map of String, Sensitive) stores secret key-value that will be used by `ApplicationSet`. For an example of how to use this in your ApplicationSet's pull request generator, see [here](https://github.com/argoproj/argo-cd/blob/master/docs/operator-manual/applicationset/Generators-Pull-Request.md#github). In this example, `tokenRef.secretName` would be application-set-secret.
- `argocd_cm` (Map of String) is aligned with the options in `argocd-cm` ConfigMap as described in the [ArgoCD Atomic Configuration](https://argo-cd.readthedocs.io/en/stable/operator-manual/declarative-setup/#atomic-configuration). For a concrete example, refer to [this documentation](https://argo-cd.readthedocs.io/en/stable/operator-manual/argocd-cm-yaml/).
- `argocd_image_updater_config` (Map of String) configures Argo CD image updater, and it is aligned with `argocd-image-updater-config` ConfigMap of Argo CD, for available options and examples, refer to [this documentation](https://argocd-image-updater.readthedocs.io/en/stable/).
- `argocd_image_updater_secret` (Map of String, Sensitive) contains sensitive data (e.g., credentials for image updater to access registries) of Argo CD image updater, for available options and examples, refer to [this documentation](https://argocd-image-updater.readthedocs.io/en/stable/).
- `argocd_image_updater_ssh_config` (Map of String) contains the ssh configuration for Argo CD image updater, and it is aligned with `argocd-image-updater-ssh-config` ConfigMap of Argo CD, for available options and examples, refer to [this documentation](https://argocd-image-updater.readthedocs.io/en/stable/).
- `argocd_notifications_cm` (Map of String) configures Argo CD notifications, and it is aligned with `argocd-notifications-cm` ConfigMap of Argo CD, for more details and examples, refer to [this documentation](https://argocd-notifications.readthedocs.io/en/stable/).
- `argocd_notifications_secret` (Map of String, Sensitive) contains sensitive data of Argo CD notifications, and it is aligned with `argocd-notifications-secret` Secret of Argo CD, for more details and examples, refer to [this documentation](https://argocd-notifications.readthedocs.io/en/stable/services/overview/#sensitive-data).
- `argocd_rbac_cm` (Map of String) is aligned with the options in `argocd-rbac-cm` ConfigMap as described in the [ArgoCD Atomic Configuration](https://argo-cd.readthedocs.io/en/stable/operator-manual/declarative-setup/#atomic-configuration). For a concrete example, refer to [this documentation](https://argo-cd.readthedocs.io/en/stable/operator-manual/argocd-rbac-cm-yaml/).
- `argocd_secret` (Map of String, Sensitive) is aligned with the options in `argocd-secret` Secret as described in the [ArgoCD Atomic Configuration](https://argo-cd.readthedocs.io/en/stable/operator-manual/declarative-setup/#atomic-configuration). For a concrete example, refer to [this documentation](https://argo-cd.readthedocs.io/en/stable/operator-manual/argocd-secret-yaml/).
- `argocd_ssh_known_hosts_cm` (Map of String) is aligned with the options in `argocd-ssh-known-hosts-cm` ConfigMap as described in the [ArgoCD Atomic Configuration](https://argo-cd.readthedocs.io/en/stable/operator-manual/declarative-setup/#atomic-configuration). For a concrete example, refer to [this documentation](https://argo-cd.readthedocs.io/en/stable/operator-manual/argocd-ssh-known-hosts-cm-yaml/).
- `argocd_tls_certs_cm` (Map of String) is aligned with the options in `argocd-tls-certs-cm` ConfigMap as described in the [ArgoCD Atomic Configuration](https://argo-cd.readthedocs.io/en/stable/operator-manual/declarative-setup/#atomic-configuration). For a concrete example, refer to [this documentation](https://argo-cd.readthedocs.io/en/stable/operator-manual/argocd-tls-certs-cm-yaml/).
- `config_management_plugins` (Attributes Map) is a map of [Config Management Plugins](https://argo-cd.readthedocs.io/en/stable/operator-manual/config-management-plugins/#config-management-plugins), the key of map entry is the `name` of the plugin, and the value is the definition of the Config Management Plugin(v2). (see [below for nested schema](#nestedatt--config_management_plugins))
- `repo_credential_secrets` (Map of Map of String, Sensitive) is a map of repo credential secrets, the key of map entry is the `name` of the secret, and the value is the aligned with options in `argocd-repositories.yaml.data` as described in the [ArgoCD Atomic Configuration](https://argo-cd.readthedocs.io/en/stable/operator-manual/declarative-setup/#atomic-configuration). For a concrete example, refer to [this documentation](https://argo-cd.readthedocs.io/en/stable/operator-manual/argocd-repositories-yaml/).
- `repo_template_credential_secrets` (Map of Map of String, Sensitive) is a map of repository credential templates secrets, the key of map entry is the `name` of the secret, and the value is the aligned with options in `argocd-repo-creds.yaml.data` as described in the [ArgoCD Atomic Configuration](https://argo-cd.readthedocs.io/en/stable/operator-manual/declarative-setup/#atomic-configuration). For a concrete example, refer to [this documentation](https://argo-cd.readthedocs.io/en/stable/operator-manual/argocd-repo-creds.yaml/).

### Read-Only

- `id` (String) Instance ID

<a id="nestedatt--argocd"></a>
### Nested Schema for `argocd`

Required:

- `spec` (Attributes) Argo CD instance spec (see [below for nested schema](#nestedatt--argocd--spec))

<a id="nestedatt--argocd--spec"></a>
### Nested Schema for `argocd.spec`

Required:

- `instance_spec` (Attributes) Argo CD instance spec (see [below for nested schema](#nestedatt--argocd--spec--instance_spec))
- `version` (String) Argo CD version. Should be equal to any Akuity [`argocd` image tag](https://quay.io/repository/akuity/argocd?tab=tags).

Optional:

- `description` (String) Instance description

<a id="nestedatt--argocd--spec--instance_spec"></a>
### Nested Schema for `argocd.spec.instance_spec`

Optional:

- `agent_permissions_rules` (Attributes List) The ability to configure agent permissions rules. (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--agent_permissions_rules))
- `app_in_any_namespace_config` (Attributes) App in any namespace config (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--app_in_any_namespace_config))
- `app_set_delegate` (Attributes) Select cluster in which you want to Install Application Set controller (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--app_set_delegate))
- `appset_plugins` (Attributes List) Application Set plugins (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--appset_plugins))
- `appset_policy` (Attributes) Configures Application Set policy settings. (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--appset_policy))
- `assistant_extension_enabled` (Boolean) Enable Powerful AI-powered assistant Extension. It helps analyze Kubernetes resources behavior and provides suggestions about resolving issues.
- `audit_extension_enabled` (Boolean) Enable Audit Extension. Set this to `true` to install Audit Extension to Argo CD instance.
- `backend_ip_allow_list_enabled` (Boolean) Enable ip allow list for cluster agents
- `cluster_customization_defaults` (Attributes) Default values for cluster agents (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--cluster_customization_defaults))
- `crossplane_extension` (Attributes) Custom Resource Definition group name that identifies the Crossplane resource in kubernetes. We will include built-in crossplane resources. Note that you can use glob pattern to match the group. ie. *.crossplane.io (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--crossplane_extension))
- `declarative_management_enabled` (Boolean) Enable Declarative Management
- `extensions` (Attributes List) Extensions (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--extensions))
- `fqdn` (String) Configures the FQDN for the argocd instance, for ingress URL, domain suffix, etc.
- `host_aliases` (Attributes List) Host Aliases that override the DNS entries for control plane Argo CD components such as API Server and Dex. (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--host_aliases))
- `image_updater_delegate` (Attributes) Select cluster in which you want to Install Image Updater (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--image_updater_delegate))
- `image_updater_enabled` (Boolean) Enable Image Updater
- `ip_allow_list` (Attributes List) IP allow list (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--ip_allow_list))
- `multi_cluster_k8s_dashboard_enabled` (Boolean) Enable the KubeVision feature
- `repo_server_delegate` (Attributes) In case some clusters don't have network access to your private Git provider you can delegate these operations to one specific cluster. (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--repo_server_delegate))
- `subdomain` (String) Instance subdomain. By default equals to instance id
- `sync_history_extension_enabled` (Boolean) Enable Sync History Extension. Sync count and duration graphs as well as event details table on Argo CD application details page.

<a id="nestedatt--argocd--spec--instance_spec--agent_permissions_rules"></a>
### Nested Schema for `argocd.spec.instance_spec.agent_permissions_rules`

Optional:

- `api_groups` (List of String) API groups of the rule.
- `resources` (List of String) Resources of the rule.
- `verbs` (List of String) Verbs of the rule.


<a id="nestedatt--argocd--spec--instance_spec--app_in_any_namespace_config"></a>
### Nested Schema for `argocd.spec.instance_spec.app_in_any_namespace_config`

Optional:

- `enabled` (Boolean) Whether the app in any namespace config is enabled or not.


<a id="nestedatt--argocd--spec--instance_spec--app_set_delegate"></a>
### Nested Schema for `argocd.spec.instance_spec.app_set_delegate`

Optional:

- `managed_cluster` (Attributes) Use managed cluster (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--app_set_delegate--managed_cluster))

<a id="nestedatt--argocd--spec--instance_spec--app_set_delegate--managed_cluster"></a>
### Nested Schema for `argocd.spec.instance_spec.app_set_delegate.managed_cluster`

Required:

- `cluster_name` (String) Cluster name



<a id="nestedatt--argocd--spec--instance_spec--appset_plugins"></a>
### Nested Schema for `argocd.spec.instance_spec.appset_plugins`

Required:

- `base_url` (String) Plugin base URL
- `name` (String) Plugin name
- `token` (String) Plugin token

Optional:

- `request_timeout` (Number) Plugin request timeout


<a id="nestedatt--argocd--spec--instance_spec--appset_policy"></a>
### Nested Schema for `argocd.spec.instance_spec.appset_policy`

Optional:

- `override_policy` (Boolean) Allows per `ApplicationSet` sync policy.
- `policy` (String) Policy restricts what types of modifications will be made to managed Argo CD `Application` resources.
Available options: `sync`, `create-only`, `create-delete`, and `create-update`.
  - Policy `sync`(default): Update and delete are allowed.
  - Policy `create-only`: Prevents ApplicationSet controller from modifying or deleting Applications.
  - Policy `create-update`: Prevents ApplicationSet controller from deleting Applications. Update is allowed.
  - Policy `create-delete`: Prevents ApplicationSet controller from modifying Applications, Delete is allowed.


<a id="nestedatt--argocd--spec--instance_spec--cluster_customization_defaults"></a>
### Nested Schema for `argocd.spec.instance_spec.cluster_customization_defaults`

Optional:

- `app_replication` (Boolean) Enables Argo CD state replication to the managed cluster that allows disconnecting the cluster from Akuity Platform without losing core Argocd features
- `auto_upgrade_disabled` (Boolean) Disable Agents Auto Upgrade. On resource update terraform will try to update the agent if this is set to `true`. Otherwise agent will update itself automatically
- `kustomization` (String) Kustomize configuration that will be applied to generated agent installation manifests
- `redis_tunneling` (Boolean) Enables the ability to connect to Redis over a web-socket tunnel that allows using Akuity agent behind HTTPS proxy


<a id="nestedatt--argocd--spec--instance_spec--crossplane_extension"></a>
### Nested Schema for `argocd.spec.instance_spec.crossplane_extension`

Optional:

- `resources` (Attributes List) Glob patterns of the resources to match. (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--crossplane_extension--resources))

<a id="nestedatt--argocd--spec--instance_spec--crossplane_extension--resources"></a>
### Nested Schema for `argocd.spec.instance_spec.crossplane_extension.resources`

Optional:

- `group` (String) Glob pattern of the group to match.



<a id="nestedatt--argocd--spec--instance_spec--extensions"></a>
### Nested Schema for `argocd.spec.instance_spec.extensions`

Required:

- `id` (String) Extension ID
- `version` (String) Extension version


<a id="nestedatt--argocd--spec--instance_spec--host_aliases"></a>
### Nested Schema for `argocd.spec.instance_spec.host_aliases`

Required:

- `ip` (String) IP address

Optional:

- `hostnames` (List of String) List of hostnames


<a id="nestedatt--argocd--spec--instance_spec--image_updater_delegate"></a>
### Nested Schema for `argocd.spec.instance_spec.image_updater_delegate`

Required:

- `control_plane` (Boolean) If use control plane or not

Optional:

- `managed_cluster` (Attributes) If use managed cluster or not (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--image_updater_delegate--managed_cluster))

<a id="nestedatt--argocd--spec--instance_spec--image_updater_delegate--managed_cluster"></a>
### Nested Schema for `argocd.spec.instance_spec.image_updater_delegate.managed_cluster`

Required:

- `cluster_name` (String) Cluster name



<a id="nestedatt--argocd--spec--instance_spec--ip_allow_list"></a>
### Nested Schema for `argocd.spec.instance_spec.ip_allow_list`

Required:

- `ip` (String) IP address

Optional:

- `description` (String) IP description


<a id="nestedatt--argocd--spec--instance_spec--repo_server_delegate"></a>
### Nested Schema for `argocd.spec.instance_spec.repo_server_delegate`

Required:

- `control_plane` (Boolean) If use control plane or not

Optional:

- `managed_cluster` (Attributes) If use managed cluster or not (see [below for nested schema](#nestedatt--argocd--spec--instance_spec--repo_server_delegate--managed_cluster))

<a id="nestedatt--argocd--spec--instance_spec--repo_server_delegate--managed_cluster"></a>
### Nested Schema for `argocd.spec.instance_spec.repo_server_delegate.managed_cluster`

Required:

- `cluster_name` (String) Cluster name






<a id="nestedatt--config_management_plugins"></a>
### Nested Schema for `config_management_plugins`

Required:

- `image` (String) Image to use for the plugin
- `spec` (Attributes) Plugin spec (see [below for nested schema](#nestedatt--config_management_plugins--spec))

Optional:

- `enabled` (Boolean) Whether this plugin is enabled or not. Default to false.

<a id="nestedatt--config_management_plugins--spec"></a>
### Nested Schema for `config_management_plugins.spec`

Required:

- `generate` (Attributes) The generate command runs in the Application source directory each time manifests are generated. Standard output must be ONLY valid Kubernetes Objects in either YAML or JSON. A non-zero exit code will fail manifest generation. Error output will be sent to the UI, so avoid printing sensitive information (such as secrets). (see [below for nested schema](#nestedatt--config_management_plugins--spec--generate))

Optional:

- `discover` (Attributes) The discovery config is applied to a repository. If every configured discovery tool matches, then the plugin may be used to generate manifests for Applications using the repository. If the discovery config is omitted then the plugin will not match any application but can still be invoked explicitly by specifying the plugin name in the app spec. Only one of fileName, find.glob, or find.command should be specified. If multiple are specified then only the first (in that order) is evaluated. (see [below for nested schema](#nestedatt--config_management_plugins--spec--discover))
- `init` (Attributes) The init command runs in the Application source directory at the beginning of each manifest generation. The init command can output anything. A non-zero status code will fail manifest generation. Init always happens immediately before generate, but its output is not treated as manifests. This is a good place to, for example, download chart dependencies. (see [below for nested schema](#nestedatt--config_management_plugins--spec--init))
- `parameters` (Attributes) The parameters config describes what parameters the UI should display for an Application. It is up to the user to actually set parameters in the Application manifest (in spec.source.plugin.parameters). The announcements only inform the "Parameters" tab in the App Details page of the UI. (see [below for nested schema](#nestedatt--config_management_plugins--spec--parameters))
- `preserve_file_mode` (Boolean) Whether the plugin receives repository files with original file mode. Dangerous since the repository might have executable files. Set to true only if you trust the CMP plugin authors. Set to false by default.
- `version` (String) Plugin version

<a id="nestedatt--config_management_plugins--spec--generate"></a>
### Nested Schema for `config_management_plugins.spec.generate`

Required:

- `command` (List of String) Command

Optional:

- `args` (List of String) Arguments of the command


<a id="nestedatt--config_management_plugins--spec--discover"></a>
### Nested Schema for `config_management_plugins.spec.discover`

Optional:

- `file_name` (String) A glob pattern (https://pkg.go.dev/path/filepath#Glob) that is applied to the Application's source directory. If there is a match, this plugin may be used for the Application.
- `find` (Attributes) Find config (see [below for nested schema](#nestedatt--config_management_plugins--spec--discover--find))

<a id="nestedatt--config_management_plugins--spec--discover--find"></a>
### Nested Schema for `config_management_plugins.spec.discover.find`

Optional:

- `args` (List of String) Arguments for the find command
- `command` (List of String) The find command runs in the repository's root directory. To match, it must exit with status code 0 and produce non-empty output to standard out.
- `glob` (String) This does the same thing as `file_name`, but it supports double-start (nested directory) glob patterns.



<a id="nestedatt--config_management_plugins--spec--init"></a>
### Nested Schema for `config_management_plugins.spec.init`

Required:

- `command` (List of String) Command

Optional:

- `args` (List of String) Arguments of the command


<a id="nestedatt--config_management_plugins--spec--parameters"></a>
### Nested Schema for `config_management_plugins.spec.parameters`

Optional:

- `dynamic` (Attributes) Dynamic parameter announcements are announcements specific to an Application handled by this plugin. For example, the values for a Helm chart's values.yaml file could be sent as parameter announcements. (see [below for nested schema](#nestedatt--config_management_plugins--spec--parameters--dynamic))
- `static` (Attributes List) Static parameter announcements are sent to the UI for all Applications handled by this plugin. Think of the `string`, `array`, and `map` values set here as defaults. It is up to the plugin author to make sure that these default values actually reflect the plugin's behavior if the user doesn't explicitly set different values for those parameters. (see [below for nested schema](#nestedatt--config_management_plugins--spec--parameters--static))

<a id="nestedatt--config_management_plugins--spec--parameters--dynamic"></a>
### Nested Schema for `config_management_plugins.spec.parameters.dynamic`

Optional:

- `args` (List of String) Arguments of the command
- `command` (List of String) The command will run in an Application's source directory. Standard output must be JSON matching the schema of the static parameter announcements list.


<a id="nestedatt--config_management_plugins--spec--parameters--static"></a>
### Nested Schema for `config_management_plugins.spec.parameters.static`

Optional:

- `array` (List of String) This field communicates the parameter's default value to the UI if the parameter is an `array`.
- `collection_type` (String) Collection Type describes what type of value this parameter accepts (string, array, or map) and allows the UI to present a form to match that type. Default is `string`. This field must be present for non-string types. It will not be inferred from the presence of an `array` or `map` field.
- `item_type` (String) Item type tells the UI how to present the parameter's value (or, for arrays and maps, values). Default is `string`. Examples of other types which may be supported in the future are `boolean` or `number`. Even if the itemType is not `string`, the parameter value from the Application spec will be sent to the plugin as a string. It's up to the plugin to do the appropriate conversion.
- `map` (Map of String) This field communicates the parameter's default value to the UI if the parameter is a `map`.
- `name` (String) Parameter name
- `required` (Boolean) Whether the Parameter is required or not. If this field is set to true, the UI will indicate to the user that they must set the value. Default to false.
- `string` (String) This field communicates the parameter's default value to the UI if the parameter is a `string`.
- `title` (String) Title and description of the parameter
- `tooltip` (String) Tooltip of the Parameter, will be shown when hovering over the title

## Import

In Terraform v1.5.0 and later, use an [`import` block](https://developer.hashicorp.com/terraform/language/import) to import the AKP instance using its `name`. For example:

```terraform
import {
  to = akp_instance.example
  id = "test"
}
```

Using `terraform import`, import AKP instance using its `name`. For example:

```shell
terraform import akp_instance.example test
```
