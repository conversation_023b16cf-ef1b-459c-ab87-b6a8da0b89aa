---
# generated by https://github.com/hashicorp/terraform-plugin-docs
page_title: "akp_kargo_agent Resource - akp"
subcategory: ""
description: |-
  Manages an AKP Kargo agent.
---

# akp_kargo_agent (Resource)

Manages an AKP Kargo agent.

## Example Usage (Basic)
```terraform
resource "akp_kargo_agent" "example-agent" {
  instance_id = akp_kargo_instance.example.id
  name        = "test-agent"
  spec = {
    data = {
      size = "small"
    }
  }
}
```

## Example Usage (Akuity-managed agent)
```terraform
resource "akp_kargo_agent" "example-agent" {
  instance_id = akp_kargo_instance.example.id
  name        = "test-agent"
  namespace   = "test-namespace"
  workspace   = "kargo-workspace"
  labels = {
    "app" = "kargo"
  }
  annotations = {
    "app" = "kargo"
  }
  spec = {
    description = "test-description"
    data = {
      size = "medium"
      // Set this to false if the agent is self-hosted, and this should not be changed anymore once it is set.
      akuity_managed = true
      # this needs to be the ArgoCD instance ID, and once it is set, it should not be changed.
      remote_argocd = "<your_argocd_instance_id>" # Replace with your actual ArgoCD instance ID
    }
  }
}
```

## Example Usage (Self-hosted agent)
```terraform
resource "akp_kargo_agent" "example-agent" {
  instance_id = akp_kargo_instance.example.id
  name        = "test-agent"
  namespace   = "test-namespace"
  workspace   = "kargo-workspace"
  labels = {
    "app" = "kargo"
  }
  annotations = {
    "app" = "kargo"
  }
  spec = {
    description = "test-description"
    data = {
      target_version = "0.5.53"
      size           = "medium"
      // Set this to false if the agent is self-hosted, and this should not be changed anymore once it is set.
      akuity_managed = false
      # this needs to be the ArgoCD instance ID, and once it is set, it should not be changed.
      remote_argocd = ""
      # this can be configured in self-hosted mode, if the remote argocd is not provided, and if this is provided, the remote argocd will be ignored.
      argocd_namespace = "argocd"
      # configure this based on the situation of self-hosted or not.
      auto_upgrade_disabled = true
      kustomization         = <<-EOT
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
images:
  - name: ghcr.io/akuity/kargo
    newName: quay.io/akuity/kargo
  - name: quay.io/akuityio/argo-rollouts
    newName: quay.io/akuity/argo-rollouts
  - name: quay.io/akuity/agent
    newName: quay.io/akuity/agent
EOT
    }
  }
}
```

<!-- schema generated by tfplugindocs -->
## Schema

### Required

- `instance_id` (String) The ID of the Kargo instance
- `name` (String) The name of the Kargo agent
- `spec` (Attributes) Spec of the Kargo agent (see [below for nested schema](#nestedatt--spec))

### Optional

- `annotations` (Map of String) Annotations
- `kube_config` (Attributes) Kubernetes connection settings. If configured, terraform will try to connect to the cluster and install the agent (see [below for nested schema](#nestedatt--kube_config))
- `labels` (Map of String) Labels
- `namespace` (String) The namespace of the Kargo agent
- `remove_agent_resources_on_destroy` (Boolean) Remove agent Kubernetes resources from the managed cluster when destroying cluster, default to `true`
- `workspace` (String) Workspace name for the Kargo agent

### Read-Only

- `id` (String) The ID of the Kargo agent

<a id="nestedatt--spec"></a>
### Nested Schema for `spec`

Required:

- `data` (Attributes) Kargo agent data (see [below for nested schema](#nestedatt--spec--data))

Optional:

- `description` (String) Description of the Kargo agent

<a id="nestedatt--spec--data"></a>
### Nested Schema for `spec.data`

Required:

- `size` (String) Cluster Size. One of `small`, `medium`, `large`

Optional:

- `akuity_managed` (Boolean) This means the agent is managed by Akuity
- `argocd_namespace` (String) Provide the namespace your Argo CD is installed in. This is only available if you self-host your Kargo agent.
- `auto_upgrade_disabled` (Boolean) Disable Agents Auto Upgrade. On resource update terraform will try to update the agent if this is set to `true`. Otherwise agent will update itself automatically
- `kustomization` (String) Kustomize configuration that will be applied to generated agent installation manifests
- `remote_argocd` (String) Remote Argo CD instance to connect to
- `target_version` (String) Target version of the agent to install on your cluster



<a id="nestedatt--kube_config"></a>
### Nested Schema for `kube_config`

Optional:

- `client_certificate` (String) PEM-encoded client certificate for TLS authentication.
- `client_key` (String, Sensitive) PEM-encoded client certificate key for TLS authentication.
- `cluster_ca_certificate` (String) PEM-encoded root certificates bundle for TLS authentication.
- `config_context` (String) Context name to load from the kube config file.
- `config_context_auth_info` (String)
- `config_context_cluster` (String)
- `config_path` (String) Path to the kube config file.
- `config_paths` (List of String) A list of paths to kube config files.
- `host` (String) The hostname (in form of URI) of Kubernetes master.
- `insecure` (Boolean) Whether server should be accessed without verifying the TLS certificate.
- `password` (String, Sensitive) The password to use for HTTP basic authentication when accessing the Kubernetes master endpoint.
- `proxy_url` (String) URL to the proxy to be used for all API requests
- `token` (String, Sensitive) Token to authenticate an service account
- `username` (String) The username to use for HTTP basic authentication when accessing the Kubernetes master endpoint.

## Import

In Terraform v1.5.0 and later, use an [`import` block](https://developer.hashicorp.com/terraform/language/import) to import the AKP Kargo agent using its `name`. For example:

```terraform
import {
  to = akp_kargo_agent.example
  id = "test"
}
```

Using `terraform import`, import AKP Kargo agent using its `name`. For example:

```shell
terraform import akp_kargo_agent.example test
```
