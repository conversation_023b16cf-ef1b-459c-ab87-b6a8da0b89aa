# Terraform Provider testing workflow.
name: Tests

# This GitHub action runs your tests for each pull request and push.
# Optionally, you can turn it on using a schedule for regular testing.
on:
  pull_request:
    paths-ignore:
      - 'README.md'
  push:
    branches:
      - "main"
    paths-ignore:
      - 'README.md'

# Testing only needs permissions to read the repository contents.
permissions:
  contents: read

jobs:
  # Ensure project builds before running testing matrix
  build:
    name: Build
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: 'go.mod'
          cache: true
      - run: go mod download
      - run: go build -v .

  generate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: 'go.mod'
          cache: true
      - uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ matrix.terraform }}
          terraform_wrapper: false
      - run: make generate
      - name: git diff
        run: |
          git diff --compact-summary --exit-code || \
            (echo; echo "Unexpected difference in directories after code generation. Run 'go generate ./...' command and commit."; exit 1)

  unit-test:
    name: Unit Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: 'go.mod'
          cache: true
      - run: make unit-test

  # Run acceptance tests in a matrix with Terraform CLI versions
  acceptance-test:
    name: Terraform Provider Acceptance Tests
    needs: unit-test
    runs-on: ubuntu-latest
    timeout-minutes: 45
    strategy:
      fail-fast: true
      max-parallel: 1
      matrix:
        # list whatever Terraform versions here you would like to support
        terraform:
          - '1.2.*'
          - '1.3.*'
          - '1.4.*'
          - '1.5.*'
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: 'go.mod'
          cache: true
      - uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ matrix.terraform }}
          terraform_wrapper: false
      - run: go mod download
      - env:
          TF_ACC: "1"
          AKUITY_API_KEY_ID: "${{ secrets.AKUITY_API_KEY_ID }}"
          AKUITY_API_KEY_SECRET: "${{ secrets.AKUITY_API_KEY_SECRET }}"
        run: make acc-test
        timeout-minutes: 10
