---
# generated by https://github.com/hashicorp/terraform-plugin-docs
page_title: "{{.Name}} {{.Type}} - {{.ProviderName}}"
subcategory: ""
description: |-
{{ .Description | plainmarkdown | trimspace | prefixlines "  " }}
---

# {{.Name}} ({{.Type}})

{{ .Description | trimspace }}

## Example Usage (Basic)
{{ tffile "./examples/resources/akp_kargo_agent/basic.tf" }}

## Example Usage (Akuity-managed agent)
{{ tffile "./examples/resources/akp_kargo_agent/akuity_managed.tf" }}

## Example Usage (Self-hosted agent)
{{ tffile "./examples/resources/akp_kargo_agent/self_hosted.tf" }}

{{ .SchemaMarkdown | trimspace }}

## Import

In Terraform v1.5.0 and later, use an [`import` block](https://developer.hashicorp.com/terraform/language/import) to import the AKP Kargo agent using its `name`. For example:

```terraform
import {
  to = akp_kargo_agent.example
  id = "test"
}
```

Using `terraform import`, import AKP Kargo agent using its `name`. For example:

```shell
terraform import akp_kargo_agent.example test
```
