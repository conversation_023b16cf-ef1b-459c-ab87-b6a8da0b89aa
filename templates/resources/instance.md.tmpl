---
# generated by https://github.com/hashicorp/terraform-plugin-docs
page_title: "{{.Name}} {{.Type}} - {{.ProviderName}}"
subcategory: ""
description: |-
{{ .Description | plainmarkdown | trimspace | prefixlines "  " }}
---

# {{.Name}} ({{.Type}})

{{ .Description | trimspace }}

{{ if .HasExample -}}
## Example Usage (Basic)
{{ tffile "./examples/resources/akp_instance/basic.tf" }}

## Example Usage (Config Management Plugins)

Please read the [Managing Config Management Plugins Guide](../guides/managing-cmps.md) before you start.

{{ tffile "./examples/resources/akp_instance/cmp.tf" }}

## Example Usage (Exhaustive)
{{ tffile "./examples/resources/akp_instance/resource.tf" }}
{{- end }}

{{ .SchemaMarkdown | trimspace }}

## Import

In Terraform v1.5.0 and later, use an [`import` block](https://developer.hashicorp.com/terraform/language/import) to import the AKP instance using its `name`. For example:

```terraform
import {
  to = akp_instance.example
  id = "test"
}
```

Using `terraform import`, import AKP instance using its `name`. For example:

```shell
terraform import akp_instance.example test
```
