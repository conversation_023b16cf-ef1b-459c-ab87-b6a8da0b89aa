---
# generated by https://github.com/hashicorp/terraform-plugin-docs
page_title: "{{.Name}} {{.Type}} - {{.ProviderName}}"
subcategory: ""
description: |-
{{ .Description | plainmarkdown | trimspace | prefixlines "  " }}
---

# {{.Name}} ({{.Type}})

{{ .Description | trimspace }}

{{ if .HasExample -}}
## Example Usage (Basic)
{{ tffile "./examples/resources/akp_cluster/basic.tf" }}

- The `instance_id` assumes you have an `akp_instance` resource named `argocd`.
- The `kube_config` assumes you have a resource named `cluster.my-cluster` that provides the `host`, `token`, `client_certificate`, `client_key`, `cluster_ca_certificate` of the Kubernetes cluster to deploy the agent into.

For a complete working example using a GKE cluster, see [akuity/examples](https://github.com/akuity/examples/tree/main/terraform/akuity).

## Example Usage (Custom agent size)
{{ tffile "./examples/resources/akp_cluster/custom_agent_size.tf" }}

## Example Usage (Auto agent size)
{{ tffile "./examples/resources/akp_cluster/auto_agent_size.tf" }}

- This example uses the `auto` agent size, which will automatically scale the agent based on the number of applications in the cluster. `auto_upgrade_disabled` cannot be set to `true` when using `auto` agent size.

## Example Usage (Exhaustive)
{{ tffile "./examples/resources/akp_cluster/resource.tf" }}
{{- end }}

{{ .SchemaMarkdown | trimspace }}

## Import

In Terraform v1.5.0 and later, use an [`import` block](https://developer.hashicorp.com/terraform/language/import) to import the AKP cluster using `instance_id` and `name` separated by a forward slash (`/`). For example:

```terraform
import {
  to = akp_cluster.example
  id = "6pzhawvy4echbd8x/test-cluster"
}
```

Using `terraform import`, import AKP cluster using `instance_id` and `name` separated by a forward slash (`/`). For example:

```shell
terraform import akp_cluster.example 6pzhawvy4echbd8x/test-cluster
```
