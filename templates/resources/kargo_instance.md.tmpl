---
# generated by https://github.com/hashicorp/terraform-plugin-docs
page_title: "{{.Name}} {{.Type}} - {{.ProviderName}}"
subcategory: ""
description: |-
{{ .Description | plainmarkdown | trimspace | prefixlines "  " }}
---

# {{.Name}} ({{.Type}})

{{ .Description | trimspace }}

{{ if .HasExample -}}
## Example Usage (Basic)
{{ tffile "./examples/resources/akp_kargo_instance/basic.tf" }}

## Example Usage (Exhaustive)
{{ tffile "./examples/resources/akp_kargo_instance/resource.tf" }}
{{- end }}

{{ .SchemaMarkdown | trimspace }}

## Import

In Terraform v1.5.0 and later, use an [`import` block](https://developer.hashicorp.com/terraform/language/import) to import the AKP Kargo instance using its `name`. For example:

```terraform
import {
  to = akp_kargo_instance.example
  id = "test"
}
```

Using `terraform import`, import AKP Kargo instance using its `name`. For example:

```shell
terraform import akp_kargo_instance.example test
```
