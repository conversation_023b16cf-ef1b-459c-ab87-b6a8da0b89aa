//go:build !acc

package akp

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/akuity/terraform-provider-akp/akp/types"
)

func TestNoNewKargoAgentDataSourceFields(t *testing.T) {
	assert.Equal(t, reflect.TypeOf(types.KargoAgent{}).<PERSON>umField(), len(getAKPKargoAgentDataSourceAttributes()))
	assert.Equal(t, reflect.TypeOf(types.KargoAgentSpec{}).<PERSON>um<PERSON>ield(), len(getAKPKargoAgentSpecDataSourceAttributes()))
	assert.Equal(t, reflect.TypeOf(types.KargoAgentData{}).<PERSON>um<PERSON>ield(), len(getAKPKargoAgentDataDataSourceAttributes()))
}
