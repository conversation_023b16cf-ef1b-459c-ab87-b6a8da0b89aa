//go:build !acc

package akp

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/akuity/terraform-provider-akp/akp/types"
)

// If this test fails, a field has been added/removed to the AKP Instance type.
// Update the schema attribute accordingly.
func TestNoNewKargoDataSourceFields(t *testing.T) {
	assert.Equal(t, reflect.TypeOf(types.KargoInstance{}).NumField(), len(getAKPKargoDataSourceAttributes()))
	assert.Equal(t, reflect.TypeOf(types.Kargo{}).<PERSON>um<PERSON><PERSON>(), len(getKargoDataSourceAttributes()))
	assert.Equal(t, reflect.TypeOf(types.KargoSpec{}).<PERSON>um<PERSON><PERSON>(), len(getKargoSpecDataSourceAttributes()))
	assert.Equal(t, reflect.TypeOf(types.KargoIPAllowListEntry{}).<PERSON><PERSON><PERSON><PERSON>(), len(getKargoIPAllowListEntryDataSourceAttributes()))
	assert.Equal(t, reflect.TypeOf(types.KargoAgentCustomization{}).<PERSON><PERSON><PERSON><PERSON>(), len(getKargoAgentCustomizationDataSourceAttributes()))
}
