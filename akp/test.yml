apiVersion: kargo.akuity.io/v1alpha1
kind: Kargo
metadata:
  name: akp-demo2
spec:
  description: ""
  kargoInstanceSpec:
    agentCustomizationDefaults:
      kustomization: null
    akuityIntelligence:
      aiSupportEngineerEnabled: true
      allowedUsernames:
      - admin
      enabled: true
    defaultShardAgent: onswgc1ncnjf80vm
  oidcConfig:
    additionalScopes: null
    adminAccount:
      enabled: false
    cliClientId: ""
    clientId: ""
    dexConfig: ""
    dexConfigSecret: null
    dexEnabled: false
    enabled: false
    issuerUrl: ""
    viewerAccount: {}
  subdomain: oe7rfas7k0iwcucg1
  version: v1.7.3-ak.0
---
apiVersion: kargo.akuity.io/v1alpha1
kind: KargoAgent
metadata:
  name: demo
  namespace: akuity
spec:
  data:
    akuityManaged: true
    autoUpgradeDisabled: false
    kustomization: null
    remoteArgocd: fxvwetmikzmmf1bj
    size: small
    targetVersion: 0.5.63
---
apiVersion: v1
data:
  adminAccountEnabled: "true"
  adminAccountTokenTtl: 24h
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/component: api
    app.kubernetes.io/instance: kargo
    app.kubernetes.io/name: kargo
  name: kargo-cm
---
apiVersion: kargo.akuity.io/v1alpha1
kind: Project
metadata:
  annotations:
    argocd.argoproj.io/sync-wave: "-3"
    argocd.argoproj.io/tracking-id: akp-demo-bootstrap-kargo:kargo.akuity.io/Project:/akp-demo
  finalizers:
  - kargo.akuity.io/finalizer
  name: akp-demo
---
apiVersion: kargo.akuity.io/v1alpha1
kind: Warehouse
metadata:
  annotations:
    argocd.argoproj.io/sync-wave: "-2"
    argocd.argoproj.io/tracking-id: akp-demo-bootstrap-kargo:kargo.akuity.io/Warehouse:akp-demo/guestbook
  name: guestbook
  namespace: akp-demo
spec:
  freightCreationPolicy: Automatic
  interval: 5m0s
  subscriptions:
  - image:
      discoveryLimit: 20
      imageSelectionStrategy: SemVer
      repoURL: ghcr.io/akuity/guestbook
      strictSemvers: true
---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  annotations:
    argocd.argoproj.io/tracking-id: akp-demo-bootstrap-kargo:kargo.akuity.io/Stage:akp-demo/crashloop
    kargo.akuity.io/argocd-context: '[{"name":"guestbook-crashloop","namespace":"argocd"}]'
  finalizers:
  - kargo.akuity.io/finalizer
  name: crashloop
  namespace: akp-demo
spec:
  promotionTemplate:
    spec:
      steps:
      - task:
          name: promote-sync-only
  requestedFreight:
  - origin:
      kind: Warehouse
      name: guestbook
    sources:
      direct: true
---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  annotations:
    argocd.argoproj.io/tracking-id: akp-demo-bootstrap-kargo:kargo.akuity.io/Stage:akp-demo/dev
    kargo.akuity.io/argocd-context: '[{"name":"guestbook-dev","namespace":"argocd"}]'
  finalizers:
  - kargo.akuity.io/finalizer
  name: dev
  namespace: akp-demo
spec:
  promotionTemplate:
    spec:
      steps:
      - task:
          name: promote
  requestedFreight:
  - origin:
      kind: Warehouse
      name: guestbook
    sources:
      direct: true
---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  annotations:
    argocd.argoproj.io/tracking-id: akp-demo-bootstrap-kargo:kargo.akuity.io/Stage:akp-demo/oom
  finalizers:
  - kargo.akuity.io/finalizer
  name: oom
  namespace: akp-demo
spec:
  promotionTemplate:
    spec:
      steps:
      - task:
          name: promote-sync-only
  requestedFreight:
  - origin:
      kind: Warehouse
      name: guestbook
    sources:
      direct: true
---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  annotations:
    argocd.argoproj.io/tracking-id: akp-demo-bootstrap-kargo:kargo.akuity.io/Stage:akp-demo/prod
  finalizers:
  - kargo.akuity.io/finalizer
  name: prod
  namespace: akp-demo
spec:
  promotionTemplate:
    spec:
      steps:
      - task:
          name: promote
  requestedFreight:
  - origin:
      kind: Warehouse
      name: guestbook
    sources:
      stages:
      - staging
---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  annotations:
    argocd.argoproj.io/tracking-id: akp-demo-bootstrap-kargo:kargo.akuity.io/Stage:akp-demo/staging
  finalizers:
  - kargo.akuity.io/finalizer
  name: staging
  namespace: akp-demo
spec:
  promotionTemplate:
    spec:
      steps:
      - task:
          name: promote
  requestedFreight:
  - origin:
      kind: Warehouse
      name: guestbook
    sources:
      stages:
      - dev
---
apiVersion: kargo.akuity.io/v1alpha1
kind: PromotionTask
metadata:
  annotations:
    argocd.argoproj.io/tracking-id: akp-demo-bootstrap-kargo:kargo.akuity.io/PromotionTask:akp-demo/promote
  name: promote
  namespace: akp-demo
spec:
  steps:
  - config:
      apps:
      - name: guestbook-${{ ctx.stage }}
        sources:
        - helm:
            images:
            - key: image.tag
              value: ${{ imageFrom("ghcr.io/akuity/guestbook").Tag }}
          repoURL: https://github.com/akuity/akp-demo.git
    uses: argocd-update
---
apiVersion: kargo.akuity.io/v1alpha1
kind: PromotionTask
metadata:
  annotations:
    argocd.argoproj.io/tracking-id: akp-demo-bootstrap-kargo:kargo.akuity.io/PromotionTask:akp-demo/promote-sync-only
  name: promote-sync-only
  namespace: akp-demo
spec:
  steps:
  - config:
      apps:
      - name: guestbook-${{ ctx.stage }}
        sources:
        - repoURL: https://github.com/akuity/akp-demo.git
    uses: argocd-update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  annotations:
    rbac.kargo.akuity.io/managed: "true"
  name: kargo-admin
  namespace: akp-demo
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: kargo-admin
subjects:
- kind: ServiceAccount
  name: kargo-admin
  namespace: akp-demo
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  annotations:
    rbac.kargo.akuity.io/managed: "true"
  name: kargo-promoter
  namespace: akp-demo
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: kargo-promoter
subjects:
- kind: ServiceAccount
  name: kargo-promoter
  namespace: akp-demo
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  annotations:
    rbac.kargo.akuity.io/managed: "true"
  name: kargo-viewer
  namespace: akp-demo
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: kargo-viewer
subjects:
- kind: ServiceAccount
  name: kargo-viewer
  namespace: akp-demo
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  annotations:
    rbac.kargo.akuity.io/managed: "true"
  name: kargo-admin
  namespace: akp-demo
rules:
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  - serviceaccounts
  verbs:
  - '*'
- apiGroups:
  - rbac.authorization.k8s.io
  resources:
  - rolebindings
  - roles
  verbs:
  - '*'
- apiGroups:
  - kargo.akuity.io
  resources:
  - freights
  - stages
  - warehouses
  - projectconfigs
  verbs:
  - '*'
- apiGroups:
  - kargo.akuity.io
  resources:
  - stages
  verbs:
  - promote
- apiGroups:
  - kargo.akuity.io
  resources:
  - promotions
  verbs:
  - create
  - delete
  - get
  - list
  - watch
- apiGroups:
  - kargo.akuity.io
  resources:
  - freights/status
  verbs:
  - patch
- apiGroups:
  - argoproj.io
  resources:
  - analysisruns
  verbs:
  - delete
  - get
  - list
  - watch
- apiGroups:
  - argoproj.io
  resources:
  - analysistemplates
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  annotations:
    rbac.kargo.akuity.io/managed: "true"
  name: kargo-promoter
  namespace: akp-demo
rules:
- apiGroups:
  - ""
  resources:
  - events
  - serviceaccounts
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - rbac.authorization.k8s.io
  resources:
  - rolebindings
  - roles
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - kargo.akuity.io
  resources:
  - freights
  - stages
  - warehouses
  - projectconfigs
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - kargo.akuity.io
  resources:
  - stages
  verbs:
  - promote
- apiGroups:
  - kargo.akuity.io
  resources:
  - promotions
  verbs:
  - create
  - get
  - list
  - watch
- apiGroups:
  - kargo.akuity.io
  resources:
  - freights/status
  verbs:
  - patch
- apiGroups:
  - argoproj.io
  resources:
  - analysisruns
  - analysistemplates
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  annotations:
    rbac.kargo.akuity.io/managed: "true"
  name: kargo-viewer
  namespace: akp-demo
rules:
- apiGroups:
  - ""
  resources:
  - events
  - serviceaccounts
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - rbac.authorization.k8s.io
  resources:
  - rolebindings
  - roles
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - kargo.akuity.io
  resources:
  - freights
  - promotions
  - stages
  - warehouses
  - projectconfigs
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - argoproj.io
  resources:
  - analysisruns
  - analysistemplates
  verbs:
  - get
  - list
  - watch
---
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    rbac.kargo.akuity.io/managed: "true"
  name: kargo-admin
  namespace: akp-demo
---
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    rbac.kargo.akuity.io/managed: "true"
  name: kargo-promoter
  namespace: akp-demo
---
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    rbac.kargo.akuity.io/managed: "true"
  name: kargo-viewer
  namespace: akp-demo
