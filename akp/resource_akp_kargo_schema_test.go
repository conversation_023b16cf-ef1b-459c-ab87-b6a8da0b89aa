//go:build !acc

package akp

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/akuity/terraform-provider-akp/akp/types"
)

// If this test fails, a field has been added/removed to the AKP Kargo Instance type.
// Update the schema attribute accordingly.
func TestNoNewKargoFields(t *testing.T) {
	assert.Equal(t, reflect.TypeOf(types.Kargo{}).NumField(), len(getKargoAttributes()))
	assert.Equal(t, reflect.TypeOf(types.KargoSpec{}).<PERSON><PERSON><PERSON><PERSON>(), len(getKargoSpecAttributes()))
	assert.Equal(t, reflect.TypeOf(types.KargoIPAllowListEntry{}).<PERSON><PERSON><PERSON><PERSON>(), len(getKargoIPAllowListEntryAttributes()))
	assert.Equal(t, reflect.TypeOf(types.KargoAgentCustomization{}).<PERSON><PERSON><PERSON><PERSON>(), len(getKargoAgentCustomizationAttributes()))
}
